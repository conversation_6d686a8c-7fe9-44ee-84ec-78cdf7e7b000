{"version": 3, "file": "twitterMinuteNow.js", "names": ["twitterMinute", "steps", "formatAs", "concat"], "sources": ["../../source/style/twitterMinuteNow.js"], "sourcesContent": ["import twitterMinute from './twitterMinute.js'\r\n\r\nexport default {\r\n\t...twitterMinute,\r\n\t// Add \"now\".\r\n\tsteps: [{ formatAs: 'now' }].concat(twitterMinute.steps)\r\n}"], "mappings": ";;;;;;AAAA,OAAOA,aAAP,MAA0B,oBAA1B;AAEA,+CACIA,aADJ;EAEC;EACAC,KAAK,EAAE,CAAC;IAAEC,QAAQ,EAAE;EAAZ,CAAD,EAAsBC,MAAtB,CAA6BH,aAAa,CAACC,KAA3C;AAHR"}