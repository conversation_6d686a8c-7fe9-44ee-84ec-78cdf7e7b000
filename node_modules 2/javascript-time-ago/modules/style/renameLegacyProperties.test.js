import renameLegacyProperties from './renameLegacyProperties.js';
describe('style/renameLegacyProperties', function () {
  it('should rename legacy properties', function () {
    renameLegacyProperties({
      steps: [{
        unit: 'now',
        minTime: {
          week: 2,
          "default": 1
        }
      }],
      labels: 'long'
    }).should.deep.equal({
      gradation: [{
        unit: 'now',
        threshold: 1,
        threshold_for_week: 2
      }],
      flavour: 'long'
    });
  });
  it('should cover edge cases', function () {
    var custom = function custom() {};

    renameLegacyProperties({
      custom: custom
    }).should.deep.equal({
      custom: custom
    });
  });
});
//# sourceMappingURL=renameLegacyProperties.test.js.map