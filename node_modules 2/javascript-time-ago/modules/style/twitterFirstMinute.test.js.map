{"version": 3, "file": "twitterFirstMinute.test.js", "names": ["twitter", "TimeAgo", "hour", "minute", "day", "month", "year", "describe", "it", "timeAgo", "format", "Date", "now", "should", "equal", "getTime", "formatInterval", "secondsPassed", "round"], "sources": ["../../source/style/twitterFirstMinute.test.js"], "sourcesContent": ["import twitter from './twitterFirstMinute.js'\r\nimport TimeAgo from '../TimeAgo.js'\r\nimport { hour, minute, day, month, year } from '../steps/index.js'\r\n\r\ndescribe('style/twitterFirstMinute', () => {\r\n\tit('should work with string name of the style', () => {\r\n\t\tconst timeAgo = new TimeAgo('en')\r\n\t\ttimeAgo.format(Date.now() - 3 * 60 * 60 * 1000, 'twitter-first-minute').should.equal('3h')\r\n\t})\r\n\r\n\tit('should format Twitter style relative time (English) (round: \"floor\")', () => {\r\n\t\tconst timeAgo = new TimeAgo('en')\r\n\r\n\t\tconst now = new Date(2016, 3, 10, 22, 59).getTime()\r\n\t\tconst formatInterval = (secondsPassed) => timeAgo.format(now - secondsPassed * 1000, { now, ...twitter, round: 'floor' })\r\n\r\n\t\tformatInterval(0).should.equal('')\r\n\t\tformatInterval(0.9).should.equal('')\r\n\t\tformatInterval(59.9).should.equal('')\r\n\t\tformatInterval(60).should.equal('1m')\r\n\t\tformatInterval(1.9 * minute).should.equal('1m')\r\n\t\tformatInterval(2 * minute).should.equal('2m')\r\n\t\tformatInterval(2.9 * minute).should.equal('2m')\r\n\t\tformatInterval(3 * minute).should.equal('3m')\r\n\t\t// …\r\n\t\tformatInterval(59.9 * minute).should.equal('59m')\r\n\t\tformatInterval(60 * minute).should.equal('1h')\r\n\t\tformatInterval(1.9 * hour).should.equal('1h')\r\n\t\tformatInterval(2 * hour).should.equal('2h')\r\n\t\tformatInterval(2.9 * hour).should.equal('2h')\r\n\t\tformatInterval(3 * hour).should.equal('3h')\r\n\t\t// …\r\n\t\tformatInterval(23.9 * hour).should.equal('23h')\r\n\t\tformatInterval(day + 2 * minute + hour).should.equal('Apr 9')\r\n\t\t// …\r\n\t\t// `month` is about 30.5 days.\r\n\t\tformatInterval(month * 3).should.equal('Jan 10')\r\n\t\tformatInterval(month * 4).should.equal('Dec 11, 2015')\r\n\t\tformatInterval(year).should.equal('Apr 11, 2015')\r\n\r\n\t\t// Test future dates.\r\n\t\t// `month` is about 30.5 days.\r\n\t\tformatInterval(-1 * month * 8).should.equal('Dec 10')\r\n\t\tformatInterval(-1 * month * 9).should.equal('Jan 9, 2017')\r\n\t})\r\n\r\n\tit('should format Twitter style relative time (English) (round: \"round\")', () => {\r\n\t\tconst timeAgo = new TimeAgo('en')\r\n\r\n\t\tconst now = new Date(2016, 3, 10, 22, 59).getTime()\r\n\t\tconst formatInterval = (secondsPassed) => timeAgo.format(now - secondsPassed * 1000, { now, ...twitter, round: 'round' })\r\n\r\n\t\tformatInterval(0).should.equal('')\r\n\t\tformatInterval(59.9).should.equal('')\r\n\t\tformatInterval(60).should.equal('1m')\r\n\t\tformatInterval(1.49 * minute).should.equal('1m')\r\n\t\tformatInterval(1.5 * minute).should.equal('2m')\r\n\t})\r\n})"], "mappings": ";;;;;;AAAA,OAAOA,OAAP,MAAoB,yBAApB;AACA,OAAOC,OAAP,MAAoB,eAApB;AACA,SAASC,IAAT,EAAeC,MAAf,EAAuBC,GAAvB,EAA4BC,KAA5B,EAAmCC,IAAnC,QAA+C,mBAA/C;AAEAC,QAAQ,CAAC,0BAAD,EAA6B,YAAM;EAC1CC,EAAE,CAAC,2CAAD,EAA8C,YAAM;IACrD,IAAMC,OAAO,GAAG,IAAIR,OAAJ,CAAY,IAAZ,CAAhB;IACAQ,OAAO,CAACC,MAAR,CAAeC,IAAI,CAACC,GAAL,KAAa,IAAI,EAAJ,GAAS,EAAT,GAAc,IAA1C,EAAgD,sBAAhD,EAAwEC,MAAxE,CAA+EC,KAA/E,CAAqF,IAArF;EACA,CAHC,CAAF;EAKAN,EAAE,CAAC,sEAAD,EAAyE,YAAM;IAChF,IAAMC,OAAO,GAAG,IAAIR,OAAJ,CAAY,IAAZ,CAAhB;IAEA,IAAMW,GAAG,GAAG,IAAID,IAAJ,CAAS,IAAT,EAAe,CAAf,EAAkB,EAAlB,EAAsB,EAAtB,EAA0B,EAA1B,EAA8BI,OAA9B,EAAZ;;IACA,IAAMC,cAAc,GAAG,SAAjBA,cAAiB,CAACC,aAAD;MAAA,OAAmBR,OAAO,CAACC,MAAR,CAAeE,GAAG,GAAGK,aAAa,GAAG,IAArC;QAA6CL,GAAG,EAAHA;MAA7C,GAAqDZ,OAArD;QAA8DkB,KAAK,EAAE;MAArE,GAAnB;IAAA,CAAvB;;IAEAF,cAAc,CAAC,CAAD,CAAd,CAAkBH,MAAlB,CAAyBC,KAAzB,CAA+B,EAA/B;IACAE,cAAc,CAAC,GAAD,CAAd,CAAoBH,MAApB,CAA2BC,KAA3B,CAAiC,EAAjC;IACAE,cAAc,CAAC,IAAD,CAAd,CAAqBH,MAArB,CAA4BC,KAA5B,CAAkC,EAAlC;IACAE,cAAc,CAAC,EAAD,CAAd,CAAmBH,MAAnB,CAA0BC,KAA1B,CAAgC,IAAhC;IACAE,cAAc,CAAC,MAAMb,MAAP,CAAd,CAA6BU,MAA7B,CAAoCC,KAApC,CAA0C,IAA1C;IACAE,cAAc,CAAC,IAAIb,MAAL,CAAd,CAA2BU,MAA3B,CAAkCC,KAAlC,CAAwC,IAAxC;IACAE,cAAc,CAAC,MAAMb,MAAP,CAAd,CAA6BU,MAA7B,CAAoCC,KAApC,CAA0C,IAA1C;IACAE,cAAc,CAAC,IAAIb,MAAL,CAAd,CAA2BU,MAA3B,CAAkCC,KAAlC,CAAwC,IAAxC,EAbgF,CAchF;;IACAE,cAAc,CAAC,OAAOb,MAAR,CAAd,CAA8BU,MAA9B,CAAqCC,KAArC,CAA2C,KAA3C;IACAE,cAAc,CAAC,KAAKb,MAAN,CAAd,CAA4BU,MAA5B,CAAmCC,KAAnC,CAAyC,IAAzC;IACAE,cAAc,CAAC,MAAMd,IAAP,CAAd,CAA2BW,MAA3B,CAAkCC,KAAlC,CAAwC,IAAxC;IACAE,cAAc,CAAC,IAAId,IAAL,CAAd,CAAyBW,MAAzB,CAAgCC,KAAhC,CAAsC,IAAtC;IACAE,cAAc,CAAC,MAAMd,IAAP,CAAd,CAA2BW,MAA3B,CAAkCC,KAAlC,CAAwC,IAAxC;IACAE,cAAc,CAAC,IAAId,IAAL,CAAd,CAAyBW,MAAzB,CAAgCC,KAAhC,CAAsC,IAAtC,EApBgF,CAqBhF;;IACAE,cAAc,CAAC,OAAOd,IAAR,CAAd,CAA4BW,MAA5B,CAAmCC,KAAnC,CAAyC,KAAzC;IACAE,cAAc,CAACZ,GAAG,GAAG,IAAID,MAAV,GAAmBD,IAApB,CAAd,CAAwCW,MAAxC,CAA+CC,KAA/C,CAAqD,OAArD,EAvBgF,CAwBhF;IACA;;IACAE,cAAc,CAACX,KAAK,GAAG,CAAT,CAAd,CAA0BQ,MAA1B,CAAiCC,KAAjC,CAAuC,QAAvC;IACAE,cAAc,CAACX,KAAK,GAAG,CAAT,CAAd,CAA0BQ,MAA1B,CAAiCC,KAAjC,CAAuC,cAAvC;IACAE,cAAc,CAACV,IAAD,CAAd,CAAqBO,MAArB,CAA4BC,KAA5B,CAAkC,cAAlC,EA5BgF,CA8BhF;IACA;;IACAE,cAAc,CAAC,CAAC,CAAD,GAAKX,KAAL,GAAa,CAAd,CAAd,CAA+BQ,MAA/B,CAAsCC,KAAtC,CAA4C,QAA5C;IACAE,cAAc,CAAC,CAAC,CAAD,GAAKX,KAAL,GAAa,CAAd,CAAd,CAA+BQ,MAA/B,CAAsCC,KAAtC,CAA4C,aAA5C;EACA,CAlCC,CAAF;EAoCAN,EAAE,CAAC,sEAAD,EAAyE,YAAM;IAChF,IAAMC,OAAO,GAAG,IAAIR,OAAJ,CAAY,IAAZ,CAAhB;IAEA,IAAMW,GAAG,GAAG,IAAID,IAAJ,CAAS,IAAT,EAAe,CAAf,EAAkB,EAAlB,EAAsB,EAAtB,EAA0B,EAA1B,EAA8BI,OAA9B,EAAZ;;IACA,IAAMC,cAAc,GAAG,SAAjBA,cAAiB,CAACC,aAAD;MAAA,OAAmBR,OAAO,CAACC,MAAR,CAAeE,GAAG,GAAGK,aAAa,GAAG,IAArC;QAA6CL,GAAG,EAAHA;MAA7C,GAAqDZ,OAArD;QAA8DkB,KAAK,EAAE;MAArE,GAAnB;IAAA,CAAvB;;IAEAF,cAAc,CAAC,CAAD,CAAd,CAAkBH,MAAlB,CAAyBC,KAAzB,CAA+B,EAA/B;IACAE,cAAc,CAAC,IAAD,CAAd,CAAqBH,MAArB,CAA4BC,KAA5B,CAAkC,EAAlC;IACAE,cAAc,CAAC,EAAD,CAAd,CAAmBH,MAAnB,CAA0BC,KAA1B,CAAgC,IAAhC;IACAE,cAAc,CAAC,OAAOb,MAAR,CAAd,CAA8BU,MAA9B,CAAqCC,KAArC,CAA2C,IAA3C;IACAE,cAAc,CAAC,MAAMb,MAAP,CAAd,CAA6BU,MAA7B,CAAoCC,KAApC,CAA0C,IAA1C;EACA,CAXC,CAAF;AAYA,CAtDO,CAAR"}