{"version": 3, "file": "mini.js", "names": ["steps", "formatAs", "labels"], "sources": ["../../source/style/mini.js"], "sourcesContent": ["export default {\r\n\tsteps: [\r\n\t\t{\r\n\t\t\tformatAs: 'second'\r\n\t\t},\r\n\t\t{\r\n\t\t\tformatAs: 'minute'\r\n\t\t},\r\n\t\t{\r\n\t\t\tformatAs: 'hour'\r\n\t\t},\r\n\t\t{\r\n\t\t\tformatAs: 'day'\r\n\t\t},\r\n\t\t{\r\n\t\t\tformatAs: 'month'\r\n\t\t},\r\n\t\t{\r\n\t\t\tformatAs: 'year'\r\n\t\t}\r\n\t],\r\n\tlabels: [\r\n\t\t// \"mini\" labels are only defined for a few languages.\r\n\t\t'mini',\r\n\t\t// \"short-time\" labels are only defined for a few languages.\r\n\t\t'short-time',\r\n\t\t// \"narrow\" and \"short\" labels are defined for all languages.\r\n\t\t// \"narrow\" labels can sometimes be weird (like \"+5d.\"),\r\n\t\t// but \"short\" labels have the \" ago\" part, so \"narrow\" seem\r\n\t\t// more appropriate.\r\n\t\t// \"short\" labels would have been more appropriate if they\r\n\t\t// didn't have the \" ago\" part, hence the \"short-time\" above.\r\n\t\t'narrow',\r\n\t\t// Since \"narrow\" labels are always present, \"short\" element\r\n\t\t// of this array can be removed.\r\n\t\t'short'\r\n\t]\r\n}"], "mappings": "AAAA,eAAe;EACdA,KAAK,EAAE,CACN;IACCC,QAAQ,EAAE;EADX,CADM,EAIN;IACCA,QAAQ,EAAE;EADX,CAJM,EAON;IACCA,QAAQ,EAAE;EADX,CAPM,EAUN;IACCA,QAAQ,EAAE;EADX,CAVM,EAaN;IACCA,QAAQ,EAAE;EADX,CAbM,EAgBN;IACCA,QAAQ,EAAE;EADX,CAhBM,CADO;EAqBdC,MAAM,EAAE,CACP;EACA,MAFO,EAGP;EACA,YAJO,EAKP;EACA;EACA;EACA;EACA;EACA;EACA,QAXO,EAYP;EACA;EACA,OAdO;AArBM,CAAf"}