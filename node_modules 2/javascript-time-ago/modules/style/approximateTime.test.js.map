{"version": 3, "file": "approximateTime.test.js", "names": ["TimeAgo", "approximateTime", "day", "month", "year", "describe", "it", "approximateScaleStepsTest", "labels", "timeAgo", "now", "Date", "elapsed", "time", "format", "approximateScaleSteps", "length", "Error", "i", "should", "equal"], "sources": ["../../source/style/approximateTime.test.js"], "sourcesContent": ["import TimeAgo from '../TimeAgo.js'\r\nimport approximateTime from './approximateTime.js'\r\nimport { day, month, year } from '../steps/units.js'\r\n\r\ndescribe('style/approximate-time', () => {\r\n\tit('should format relative time (English)', () => {\r\n\t\tapproximateScaleStepsTest([\r\n\t\t\t'just now',\r\n\t\t\t'1 minute',\r\n\t\t\t'2 minutes',\r\n\t\t\t'5 minutes',\r\n\t\t\t'10 minutes',\r\n\t\t\t'15 minutes',\r\n\t\t\t'20 minutes',\r\n\t\t\t'25 minutes',\r\n\t\t\t'30 minutes',\r\n\t\t\t'35 minutes',\r\n\t\t\t'40 minutes',\r\n\t\t\t'45 minutes',\r\n\t\t\t'50 minutes',\r\n\t\t\t'1 hour',\r\n\t\t\t'2 hours',\r\n\t\t\t'3 hours',\r\n\t\t\t'4 hours',\r\n\t\t\t'5 hours',\r\n\t\t\t'6 hours',\r\n\t\t\t'7 hours',\r\n\t\t\t'8 hours',\r\n\t\t\t'9 hours',\r\n\t\t\t'10 hours',\r\n\t\t\t'11 hours',\r\n\t\t\t'12 hours',\r\n\t\t\t'13 hours',\r\n\t\t\t'14 hours',\r\n\t\t\t'15 hours',\r\n\t\t\t'16 hours',\r\n\t\t\t'17 hours',\r\n\t\t\t'18 hours',\r\n\t\t\t'19 hours',\r\n\t\t\t'20 hours',\r\n\t\t\t'1 day',\r\n\t\t\t'2 days',\r\n\t\t\t'3 days',\r\n\t\t\t'4 days',\r\n\t\t\t'5 days',\r\n\t\t\t'1 week',\r\n\t\t\t'2 weeks',\r\n\t\t\t'3 weeks',\r\n\t\t\t'1 month',\r\n\t\t\t'2 months',\r\n\t\t\t'3 months',\r\n\t\t\t'4 months',\r\n\t\t\t'5 months',\r\n\t\t\t'6 months',\r\n\t\t\t'7 months',\r\n\t\t\t'8 months',\r\n\t\t\t'9 months',\r\n\t\t\t'9 months',\r\n\t\t\t'10 months',\r\n\t\t\t'1 year',\r\n\t\t\t'2 years',\r\n\t\t\t'3 years',\r\n\t\t\t'100 years'\r\n\t\t],\r\n\t\t'en-US')\r\n\t})\r\n\r\n\tit('should format relative time (Russian)', () => {\r\n\t\tapproximateScaleStepsTest([\r\n\t\t\t'только что',\r\n\t\t\t'1 минута',\r\n\t\t\t'2 минуты',\r\n\t\t\t'5 минут',\r\n\t\t\t'10 минут',\r\n\t\t\t'15 минут',\r\n\t\t\t'20 минут',\r\n\t\t\t'25 минут',\r\n\t\t\t'30 минут',\r\n\t\t\t'35 минут',\r\n\t\t\t'40 минут',\r\n\t\t\t'45 минут',\r\n\t\t\t'50 минут',\r\n\t\t\t'1 час',\r\n\t\t\t'2 часа',\r\n\t\t\t'3 часа',\r\n\t\t\t'4 часа',\r\n\t\t\t'5 часов',\r\n\t\t\t'6 часов',\r\n\t\t\t'7 часов',\r\n\t\t\t'8 часов',\r\n\t\t\t'9 часов',\r\n\t\t\t'10 часов',\r\n\t\t\t'11 часов',\r\n\t\t\t'12 часов',\r\n\t\t\t'13 часов',\r\n\t\t\t'14 часов',\r\n\t\t\t'15 часов',\r\n\t\t\t'16 часов',\r\n\t\t\t'17 часов',\r\n\t\t\t'18 часов',\r\n\t\t\t'19 часов',\r\n\t\t\t'20 часов',\r\n\t\t\t'1 день',\r\n\t\t\t'2 дня',\r\n\t\t\t'3 дня',\r\n\t\t\t'4 дня',\r\n\t\t\t'5 дней',\r\n\t\t\t'1 неделю',\r\n\t\t\t'2 недели',\r\n\t\t\t'3 недели',\r\n\t\t\t'1 месяц',\r\n\t\t\t'2 месяца',\r\n\t\t\t'3 месяца',\r\n\t\t\t'4 месяца',\r\n\t\t\t'5 месяцев',\r\n\t\t\t'6 месяцев',\r\n\t\t\t'7 месяцев',\r\n\t\t\t'8 месяцев',\r\n\t\t\t'9 месяцев',\r\n\t\t\t'9 месяцев',\r\n\t\t\t'10 месяцев',\r\n\t\t\t'1 год',\r\n\t\t\t'2 года',\r\n\t\t\t'3 года',\r\n\t\t\t'100 лет'\r\n\t\t],\r\n\t\t'ru-RU')\r\n\t})\r\n})\r\n\r\nfunction approximateScaleStepsTest(labels, timeAgo) {\r\n\tif (typeof timeAgo === 'string') {\r\n\t\ttimeAgo = new TimeAgo(timeAgo)\r\n\t}\r\n\r\n\tconst now = Date.now()\r\n\tconst elapsed = time => timeAgo.format(now - time * 1000, 'approximate-time', { now })\r\n\r\n\tif (approximateScaleSteps.length !== labels.length) {\r\n\t\tthrow new Error(`Array length mismatch. Steps: ${approximateScaleSteps.length}, labels: ${labels.length}`)\r\n\t}\r\n\r\n\tlet i = 0\r\n\twhile (i < approximateScaleSteps.length) {\r\n\t\tfor (let time of approximateScaleSteps[i]) {\r\n\t\t\telapsed(time).should.equal(labels[i])\r\n\t\t}\r\n\t\ti++\r\n\t}\r\n}\r\n\r\nconst approximateScaleSteps =\r\n[\r\n\t// 'just now':\r\n\t[\r\n\t\t0,\r\n\t\t40.49\r\n\t],\r\n\t// '1 minute ago':\r\n\t[\r\n\t\t45.5,\r\n\t\t1.49 * 60\r\n\t],\r\n\t// '2 minutes ago':\r\n\t[\r\n\t\t1.51 * 60,\r\n\t\t2.49 * 60\r\n\t],\r\n\t// '5 minutes ago':\r\n\t[\r\n\t\t2.51 * 60,\r\n\t\t7.49 * 60\r\n\t],\r\n\t// '10 minutes ago':\r\n\t[\r\n\t\t7.51  * 60,\r\n\t\t12.49 * 60\r\n\t],\r\n\t// '15 minutes ago':\r\n\t[\r\n\t\t12.51 * 60,\r\n\t\t17.49 * 60\r\n\t],\r\n\t// '20 minutes ago':\r\n\t[\r\n\t\t17.51 * 60,\r\n\t\t22.49 * 60\r\n\t],\r\n\t// '25 minutes ago':\r\n\t[\r\n\t\t22.51 * 60,\r\n\t\t27.49 * 60\r\n\t],\r\n\t// '30 minutes ago':\r\n\t[\r\n\t\t27.51 * 60,\r\n\t\t32.49 * 60\r\n\t],\r\n\t// '35 minutes ago':\r\n\t[\r\n\t\t32.51 * 60,\r\n\t\t37.49 * 60\r\n\t],\r\n\t// '40 minutes ago':\r\n\t[\r\n\t\t37.51 * 60,\r\n\t\t42.49 * 60\r\n\t],\r\n\t// '45 minutes ago':\r\n\t[\r\n\t\t42.51 * 60,\r\n\t\t47.49 * 60\r\n\t],\r\n\t// '50 minutes ago':\r\n\t[\r\n\t\t47.51 * 60,\r\n\t\t52.49 * 60\r\n\t],\r\n\t// '1 hour ago':\r\n\t[\r\n\t\t55.01 * 60,\r\n\t\t1.49  * 60 * 60\r\n\t],\r\n\t// '2 hours ago':\r\n\t[\r\n\t\t1.51  * 60 * 60,\r\n\t\t2.49  * 60 * 60\r\n\t],\r\n\t// '3 hours ago':\r\n\t[\r\n\t\t2.51  * 60 * 60,\r\n\t\t3.49  * 60 * 60\r\n\t],\r\n\t// '4 hours ago':\r\n\t[\r\n\t\t3.51  * 60 * 60,\r\n\t\t4.49  * 60 * 60\r\n\t],\r\n\t// '5 hours ago':\r\n\t[\r\n\t\t4.51  * 60 * 60,\r\n\t\t5.49  * 60 * 60\r\n\t],\r\n\t// '6 hours ago':\r\n\t[\r\n\t\t5.51  * 60 * 60,\r\n\t\t6.49  * 60 * 60\r\n\t],\r\n\t// '7 hours ago':\r\n\t[\r\n\t\t6.51  * 60 * 60,\r\n\t\t7.49  * 60 * 60\r\n\t],\r\n\t// '8 hours ago':\r\n\t[\r\n\t\t7.51  * 60 * 60,\r\n\t\t8.49  * 60 * 60\r\n\t],\r\n\t// '9 hours ago':\r\n\t[\r\n\t\t8.51  * 60 * 60,\r\n\t\t9.49  * 60 * 60\r\n\t],\r\n\t// '10 hours ago':\r\n\t[\r\n\t\t9.51  * 60 * 60,\r\n\t\t10.49 * 60 * 60\r\n\t],\r\n\t// '11 hours ago':\r\n\t[\r\n\t\t10.51 * 60 * 60,\r\n\t\t11.49 * 60 * 60\r\n\t],\r\n\t// '12 hours ago':\r\n\t[\r\n\t\t11.51 * 60 * 60,\r\n\t\t12.49 * 60 * 60\r\n\t],\r\n\t// '13 hours ago':\r\n\t[\r\n\t\t12.51 * 60 * 60,\r\n\t\t13.49 * 60 * 60\r\n\t],\r\n\t// '14 hours ago':\r\n\t[\r\n\t\t13.51 * 60 * 60,\r\n\t\t14.49 * 60 * 60\r\n\t],\r\n\t// '15 hours ago':\r\n\t[\r\n\t\t14.51 * 60 * 60,\r\n\t\t15.49 * 60 * 60\r\n\t],\r\n\t// '16 hours ago':\r\n\t[\r\n\t\t15.51 * 60 * 60,\r\n\t\t16.49 * 60 * 60\r\n\t],\r\n\t// '17 hours ago':\r\n\t[\r\n\t\t16.51 * 60 * 60,\r\n\t\t17.49 * 60 * 60\r\n\t],\r\n\t// '18 hours ago':\r\n\t[\r\n\t\t17.51 * 60 * 60,\r\n\t\t18.49 * 60 * 60\r\n\t],\r\n\t// '19 hours ago':\r\n\t[\r\n\t\t18.51 * 60 * 60,\r\n\t\t19.49 * 60 * 60\r\n\t],\r\n\t// '20 hours ago':\r\n\t[\r\n\t\t19.51 * 60 * 60,\r\n\t\t20.49 * 60 * 60\r\n\t],\r\n\t// '1 day ago':\r\n\t[\r\n\t\t20.51 * 60 * 60,\r\n\t\t1.49  * day\r\n\t],\r\n\t// '2 days ago':\r\n\t[\r\n\t\t1.51  * day,\r\n\t\t2.49  * day\r\n\t],\r\n\t// '3 days ago':\r\n\t[\r\n\t\t2.51  * day,\r\n\t\t3.49  * day\r\n\t],\r\n\t// '4 days ago':\r\n\t[\r\n\t\t3.51  * day,\r\n\t\t4.49  * day\r\n\t],\r\n\t// '5 days ago':\r\n\t[\r\n\t\t4.51  * day,\r\n\t\t5.49  * day\r\n\t],\r\n\t// '1 week ago':\r\n\t[\r\n\t\t5.51  * day,\r\n\t\t1.49  * 7 * day\r\n\t],\r\n\t// '2 weeks ago':\r\n\t[\r\n\t\t1.51  * 7 * day,\r\n\t\t2.49  * 7 * day\r\n\t],\r\n\t// '3 weeks ago':\r\n\t[\r\n\t\t2.51  * 7 * day,\r\n\t\t3.49  * 7 * day\r\n\t],\r\n\t// '1 month ago':\r\n\t[\r\n\t\t3.51  * 7 * day,\r\n\t\t1.49  * month\r\n\t],\r\n\t// '2 months ago':\r\n\t[\r\n\t\t1.51  * month,\r\n\t\t2.49  * month\r\n\t],\r\n\t// '3 months ago':\r\n\t[\r\n\t\t2.51  * month,\r\n\t\t3.49  * month\r\n\t],\r\n\t// '4 months ago':\r\n\t[\r\n\t\t3.51  * month,\r\n\t\t4.49  * month\r\n\t],\r\n\t// '5 months ago':\r\n\t[\r\n\t\t4.51  * month,\r\n\t\t5.49  * month\r\n\t],\r\n\t// '6 months ago':\r\n\t[\r\n\t\t5.51  * month,\r\n\t\t6.49  * month\r\n\t],\r\n\t// '7 months ago':\r\n\t[\r\n\t\t6.51  * month,\r\n\t\t7.49  * month\r\n\t],\r\n\t// '8 months ago':\r\n\t[\r\n\t\t7.51  * month,\r\n\t\t8.49  * month\r\n\t],\r\n\t// '9 months ago':\r\n\t[\r\n\t\t8.51  * month,\r\n\t\t8.99  * month\r\n\t],\r\n\t// '9 months ago':\r\n\t[\r\n\t\t9.01  * month,\r\n\t\t9.49  * month\r\n\t],\r\n\t// '10 months ago':\r\n\t[\r\n\t\t9.51  * month,\r\n\t\t10.49  * month\r\n\t],\r\n\t// '1 year ago':\r\n\t[\r\n\t\t10.51 * month,\r\n\t\t1.49  * year\r\n\t],\r\n\t// '2 years ago':\r\n\t[\r\n\t\t1.51  * year,\r\n\t\t2.49  * year\r\n\t],\r\n\t// '3 years ago':\r\n\t[\r\n\t\t2.51  * year,\r\n\t\t3.49  * year\r\n\t],\r\n\t// '100 years ago':\r\n\t[\r\n\t\t99.51  * year,\r\n\t\t100.49 * year\r\n\t]\r\n]"], "mappings": ";;;;;;AAAA,OAAOA,OAAP,MAAoB,eAApB;AACA,OAAOC,eAAP,MAA4B,sBAA5B;AACA,SAASC,GAAT,EAAcC,KAAd,EAAqBC,IAArB,QAAiC,mBAAjC;AAEAC,QAAQ,CAAC,wBAAD,EAA2B,YAAM;EACxCC,EAAE,CAAC,uCAAD,EAA0C,YAAM;IACjDC,yBAAyB,CAAC,CACzB,UADyB,EAEzB,UAFyB,EAGzB,WAHyB,EAIzB,WAJyB,EAKzB,YALyB,EAMzB,YANyB,EAOzB,YAPyB,EAQzB,YARyB,EASzB,YATyB,EAUzB,YAVyB,EAWzB,YAXyB,EAYzB,YAZyB,EAazB,YAbyB,EAczB,QAdyB,EAezB,SAfyB,EAgBzB,SAhByB,EAiBzB,SAjByB,EAkBzB,SAlByB,EAmBzB,SAnByB,EAoBzB,SApByB,EAqBzB,SArByB,EAsBzB,SAtByB,EAuBzB,UAvByB,EAwBzB,UAxByB,EAyBzB,UAzByB,EA0BzB,UA1ByB,EA2BzB,UA3ByB,EA4BzB,UA5ByB,EA6BzB,UA7ByB,EA8BzB,UA9ByB,EA+BzB,UA/ByB,EAgCzB,UAhCyB,EAiCzB,UAjCyB,EAkCzB,OAlCyB,EAmCzB,QAnCyB,EAoCzB,QApCyB,EAqCzB,QArCyB,EAsCzB,QAtCyB,EAuCzB,QAvCyB,EAwCzB,SAxCyB,EAyCzB,SAzCyB,EA0CzB,SA1CyB,EA2CzB,UA3CyB,EA4CzB,UA5CyB,EA6CzB,UA7CyB,EA8CzB,UA9CyB,EA+CzB,UA/CyB,EAgDzB,UAhDyB,EAiDzB,UAjDyB,EAkDzB,UAlDyB,EAmDzB,UAnDyB,EAoDzB,WApDyB,EAqDzB,QArDyB,EAsDzB,SAtDyB,EAuDzB,SAvDyB,EAwDzB,WAxDyB,CAAD,EA0DzB,OA1DyB,CAAzB;EA2DA,CA5DC,CAAF;EA8DAD,EAAE,CAAC,uCAAD,EAA0C,YAAM;IACjDC,yBAAyB,CAAC,CACzB,YADyB,EAEzB,UAFyB,EAGzB,UAHyB,EAIzB,SAJyB,EAKzB,UALyB,EAMzB,UANyB,EAOzB,UAPyB,EAQzB,UARyB,EASzB,UATyB,EAUzB,UAVyB,EAWzB,UAXyB,EAYzB,UAZyB,EAazB,UAbyB,EAczB,OAdyB,EAezB,QAfyB,EAgBzB,QAhByB,EAiBzB,QAjByB,EAkBzB,SAlByB,EAmBzB,SAnByB,EAoBzB,SApByB,EAqBzB,SArByB,EAsBzB,SAtByB,EAuBzB,UAvByB,EAwBzB,UAxByB,EAyBzB,UAzByB,EA0BzB,UA1ByB,EA2BzB,UA3ByB,EA4BzB,UA5ByB,EA6BzB,UA7ByB,EA8BzB,UA9ByB,EA+BzB,UA/ByB,EAgCzB,UAhCyB,EAiCzB,UAjCyB,EAkCzB,QAlCyB,EAmCzB,OAnCyB,EAoCzB,OApCyB,EAqCzB,OArCyB,EAsCzB,QAtCyB,EAuCzB,UAvCyB,EAwCzB,UAxCyB,EAyCzB,UAzCyB,EA0CzB,SA1CyB,EA2CzB,UA3CyB,EA4CzB,UA5CyB,EA6CzB,UA7CyB,EA8CzB,WA9CyB,EA+CzB,WA/CyB,EAgDzB,WAhDyB,EAiDzB,WAjDyB,EAkDzB,WAlDyB,EAmDzB,WAnDyB,EAoDzB,YApDyB,EAqDzB,OArDyB,EAsDzB,QAtDyB,EAuDzB,QAvDyB,EAwDzB,SAxDyB,CAAD,EA0DzB,OA1DyB,CAAzB;EA2DA,CA5DC,CAAF;AA6DA,CA5HO,CAAR;;AA8HA,SAASA,yBAAT,CAAmCC,MAAnC,EAA2CC,OAA3C,EAAoD;EACnD,IAAI,OAAOA,OAAP,KAAmB,QAAvB,EAAiC;IAChCA,OAAO,GAAG,IAAIT,OAAJ,CAAYS,OAAZ,CAAV;EACA;;EAED,IAAMC,GAAG,GAAGC,IAAI,CAACD,GAAL,EAAZ;;EACA,IAAME,OAAO,GAAG,SAAVA,OAAU,CAAAC,IAAI;IAAA,OAAIJ,OAAO,CAACK,MAAR,CAAeJ,GAAG,GAAGG,IAAI,GAAG,IAA5B,EAAkC,kBAAlC,EAAsD;MAAEH,GAAG,EAAHA;IAAF,CAAtD,CAAJ;EAAA,CAApB;;EAEA,IAAIK,qBAAqB,CAACC,MAAtB,KAAiCR,MAAM,CAACQ,MAA5C,EAAoD;IACnD,MAAM,IAAIC,KAAJ,yCAA2CF,qBAAqB,CAACC,MAAjE,uBAAoFR,MAAM,CAACQ,MAA3F,EAAN;EACA;;EAED,IAAIE,CAAC,GAAG,CAAR;;EACA,OAAOA,CAAC,GAAGH,qBAAqB,CAACC,MAAjC,EAAyC;IACxC,qDAAiBD,qBAAqB,CAACG,CAAD,CAAtC,wCAA2C;MAAA,IAAlCL,IAAkC;MAC1CD,OAAO,CAACC,IAAD,CAAP,CAAcM,MAAd,CAAqBC,KAArB,CAA2BZ,MAAM,CAACU,CAAD,CAAjC;IACA;;IACDA,CAAC;EACD;AACD;;AAED,IAAMH,qBAAqB,GAC3B,CACC;AACA,CACC,CADD,EAEC,KAFD,CAFD,EAMC;AACA,CACC,IADD,EAEC,OAAO,EAFR,CAPD,EAWC;AACA,CACC,OAAO,EADR,EAEC,OAAO,EAFR,CAZD,EAgBC;AACA,CACC,OAAO,EADR,EAEC,OAAO,EAFR,CAjBD,EAqBC;AACA,CACC,OAAQ,EADT,EAEC,QAAQ,EAFT,CAtBD,EA0BC;AACA,CACC,QAAQ,EADT,EAEC,QAAQ,EAFT,CA3BD,EA+BC;AACA,CACC,QAAQ,EADT,EAEC,QAAQ,EAFT,CAhCD,EAoCC;AACA,CACC,QAAQ,EADT,EAEC,QAAQ,EAFT,CArCD,EAyCC;AACA,CACC,QAAQ,EADT,EAEC,QAAQ,EAFT,CA1CD,EA8CC;AACA,CACC,QAAQ,EADT,EAEC,QAAQ,EAFT,CA/CD,EAmDC;AACA,CACC,QAAQ,EADT,EAEC,QAAQ,EAFT,CApDD,EAwDC;AACA,CACC,QAAQ,EADT,EAEC,QAAQ,EAFT,CAzDD,EA6DC;AACA,CACC,QAAQ,EADT,EAEC,QAAQ,EAFT,CA9DD,EAkEC;AACA,CACC,QAAQ,EADT,EAEC,OAAQ,EAAR,GAAa,EAFd,CAnED,EAuEC;AACA,CACC,OAAQ,EAAR,GAAa,EADd,EAEC,OAAQ,EAAR,GAAa,EAFd,CAxED,EA4EC;AACA,CACC,OAAQ,EAAR,GAAa,EADd,EAEC,OAAQ,EAAR,GAAa,EAFd,CA7ED,EAiFC;AACA,CACC,OAAQ,EAAR,GAAa,EADd,EAEC,OAAQ,EAAR,GAAa,EAFd,CAlFD,EAsFC;AACA,CACC,OAAQ,EAAR,GAAa,EADd,EAEC,OAAQ,EAAR,GAAa,EAFd,CAvFD,EA2FC;AACA,CACC,OAAQ,EAAR,GAAa,EADd,EAEC,OAAQ,EAAR,GAAa,EAFd,CA5FD,EAgGC;AACA,CACC,OAAQ,EAAR,GAAa,EADd,EAEC,OAAQ,EAAR,GAAa,EAFd,CAjGD,EAqGC;AACA,CACC,OAAQ,EAAR,GAAa,EADd,EAEC,OAAQ,EAAR,GAAa,EAFd,CAtGD,EA0GC;AACA,CACC,OAAQ,EAAR,GAAa,EADd,EAEC,OAAQ,EAAR,GAAa,EAFd,CA3GD,EA+GC;AACA,CACC,OAAQ,EAAR,GAAa,EADd,EAEC,QAAQ,EAAR,GAAa,EAFd,CAhHD,EAoHC;AACA,CACC,QAAQ,EAAR,GAAa,EADd,EAEC,QAAQ,EAAR,GAAa,EAFd,CArHD,EAyHC;AACA,CACC,QAAQ,EAAR,GAAa,EADd,EAEC,QAAQ,EAAR,GAAa,EAFd,CA1HD,EA8HC;AACA,CACC,QAAQ,EAAR,GAAa,EADd,EAEC,QAAQ,EAAR,GAAa,EAFd,CA/HD,EAmIC;AACA,CACC,QAAQ,EAAR,GAAa,EADd,EAEC,QAAQ,EAAR,GAAa,EAFd,CApID,EAwIC;AACA,CACC,QAAQ,EAAR,GAAa,EADd,EAEC,QAAQ,EAAR,GAAa,EAFd,CAzID,EA6IC;AACA,CACC,QAAQ,EAAR,GAAa,EADd,EAEC,QAAQ,EAAR,GAAa,EAFd,CA9ID,EAkJC;AACA,CACC,QAAQ,EAAR,GAAa,EADd,EAEC,QAAQ,EAAR,GAAa,EAFd,CAnJD,EAuJC;AACA,CACC,QAAQ,EAAR,GAAa,EADd,EAEC,QAAQ,EAAR,GAAa,EAFd,CAxJD,EA4JC;AACA,CACC,QAAQ,EAAR,GAAa,EADd,EAEC,QAAQ,EAAR,GAAa,EAFd,CA7JD,EAiKC;AACA,CACC,QAAQ,EAAR,GAAa,EADd,EAEC,QAAQ,EAAR,GAAa,EAFd,CAlKD,EAsKC;AACA,CACC,QAAQ,EAAR,GAAa,EADd,EAEC,OAAQb,GAFT,CAvKD,EA2KC;AACA,CACC,OAAQA,GADT,EAEC,OAAQA,GAFT,CA5KD,EAgLC;AACA,CACC,OAAQA,GADT,EAEC,OAAQA,GAFT,CAjLD,EAqLC;AACA,CACC,OAAQA,GADT,EAEC,OAAQA,GAFT,CAtLD,EA0LC;AACA,CACC,OAAQA,GADT,EAEC,OAAQA,GAFT,CA3LD,EA+LC;AACA,CACC,OAAQA,GADT,EAEC,OAAQ,CAAR,GAAYA,GAFb,CAhMD,EAoMC;AACA,CACC,OAAQ,CAAR,GAAYA,GADb,EAEC,OAAQ,CAAR,GAAYA,GAFb,CArMD,EAyMC;AACA,CACC,OAAQ,CAAR,GAAYA,GADb,EAEC,OAAQ,CAAR,GAAYA,GAFb,CA1MD,EA8MC;AACA,CACC,OAAQ,CAAR,GAAYA,GADb,EAEC,OAAQC,KAFT,CA/MD,EAmNC;AACA,CACC,OAAQA,KADT,EAEC,OAAQA,KAFT,CApND,EAwNC;AACA,CACC,OAAQA,KADT,EAEC,OAAQA,KAFT,CAzND,EA6NC;AACA,CACC,OAAQA,KADT,EAEC,OAAQA,KAFT,CA9ND,EAkOC;AACA,CACC,OAAQA,KADT,EAEC,OAAQA,KAFT,CAnOD,EAuOC;AACA,CACC,OAAQA,KADT,EAEC,OAAQA,KAFT,CAxOD,EA4OC;AACA,CACC,OAAQA,KADT,EAEC,OAAQA,KAFT,CA7OD,EAiPC;AACA,CACC,OAAQA,KADT,EAEC,OAAQA,KAFT,CAlPD,EAsPC;AACA,CACC,OAAQA,KADT,EAEC,OAAQA,KAFT,CAvPD,EA2PC;AACA,CACC,OAAQA,KADT,EAEC,OAAQA,KAFT,CA5PD,EAgQC;AACA,CACC,OAAQA,KADT,EAEC,QAASA,KAFV,CAjQD,EAqQC;AACA,CACC,QAAQA,KADT,EAEC,OAAQC,IAFT,CAtQD,EA0QC;AACA,CACC,OAAQA,IADT,EAEC,OAAQA,IAFT,CA3QD,EA+QC;AACA,CACC,OAAQA,IADT,EAEC,OAAQA,IAFT,CAhRD,EAoRC;AACA,CACC,QAASA,IADV,EAEC,SAASA,IAFV,CArRD,CADA"}