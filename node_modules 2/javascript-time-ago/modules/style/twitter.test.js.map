{"version": 3, "file": "twitter.test.js", "names": ["twitter", "TimeAgo", "hour", "minute", "day", "month", "year", "describe", "it", "timeAgo", "format", "Date", "now", "should", "include", "formatDatePastBy", "secondsPassed", "equal", "round", "getTime", "test", "time", "result", "date", "getTimeToNextUpdate", "deep"], "sources": ["../../source/style/twitter.test.js"], "sourcesContent": ["import twitter from './twitter.js'\r\nimport TimeAgo from '../TimeAgo.js'\r\nimport { hour, minute, day, month, year } from '../steps/index.js'\r\n\r\ndescribe('style/twitter', () => {\r\n\tit('should fallback from \"mini\" to \"narrow\"', () => {\r\n\t\tconst timeAgo = new TimeAgo('ccp')\r\n\t\ttimeAgo.format(Date.now() - 3 * hour * 1000, 'twitter').should.include(' 𑄊𑄮𑄚𑄴𑄓 𑄃𑄉𑄬')\r\n\t})\r\n\r\n\tit('should format Twitter style relative time (English) (round: \"round\")', () => {\r\n\t\tconst timeAgo = new TimeAgo('en')\r\n\r\n\t\tconst formatDatePastBy = (secondsPassed) => timeAgo.format(-secondsPassed * 1000, { now: 0, ...twitter })\r\n\r\n\t\tformatDatePastBy(0.49).should.equal('0s')\r\n\t\tformatDatePastBy(0.5).should.equal('1s')\r\n\t\tformatDatePastBy(59.49).should.equal('59s')\r\n\t\tformatDatePastBy(59.5).should.equal('1m')\r\n\t\tformatDatePastBy(1.49 * minute).should.equal('1m')\r\n\t\tformatDatePastBy(1.5 * minute).should.equal('2m')\r\n\t\t// …\r\n\t\tformatDatePastBy(59.49 * minute).should.equal('59m')\r\n\t\tformatDatePastBy(59.5 * minute).should.equal('1h')\r\n\t\tformatDatePastBy(1.49 * hour).should.equal('1h')\r\n\t\tformatDatePastBy(1.5 * hour).should.equal('2h')\r\n\t\t// …\r\n\t\tformatDatePastBy(23.49 * hour).should.equal('23h')\r\n\t})\r\n\r\n\tit('should format Twitter style relative time (English) (round: \"floor\")', () => {\r\n\t\tconst timeAgo = new TimeAgo('en')\r\n\r\n\t\tconst formatDatePastBy = (secondsPassed) => timeAgo.format(-secondsPassed * 1000, { now: 0, ...twitter, round: 'floor' })\r\n\r\n\t\tformatDatePastBy(0).should.equal('0s')\r\n\t\tformatDatePastBy(0.9).should.equal('0s')\r\n\t\tformatDatePastBy(1).should.equal('1s')\r\n\t\tformatDatePastBy(59.9).should.equal('59s')\r\n\t\tformatDatePastBy(60).should.equal('1m')\r\n\t\tformatDatePastBy(1.9 * minute).should.equal('1m')\r\n\t\tformatDatePastBy(2 * minute).should.equal('2m')\r\n\t\tformatDatePastBy(2.9 * minute).should.equal('2m')\r\n\t\tformatDatePastBy(3 * minute).should.equal('3m')\r\n\t\t// …\r\n\t\tformatDatePastBy(59.9 * minute).should.equal('59m')\r\n\t\tformatDatePastBy(60 * minute).should.equal('1h')\r\n\t\tformatDatePastBy(1.9 * hour).should.equal('1h')\r\n\t\tformatDatePastBy(2 * hour).should.equal('2h')\r\n\t\tformatDatePastBy(2.9 * hour).should.equal('2h')\r\n\t\tformatDatePastBy(3 * hour).should.equal('3h')\r\n\t\t// …\r\n\t\tformatDatePastBy(23.9 * hour).should.equal('23h')\r\n\t})\r\n\r\n\tit('should format Twitter style relative time (English) (absolute dates)', () => {\r\n\t\tconst timeAgo = new TimeAgo('en')\r\n\r\n\t\t// April 10th, 2016, 12:00.\r\n\t\tconst now = new Date(2016, 3, 10, 12, 0).getTime()\r\n\t\tconst formatDatePastBy = (secondsPassed) => timeAgo.format(now - secondsPassed * 1000, { now, ...twitter })\r\n\r\n\t\tformatDatePastBy(day + 2 * minute + hour).should.equal('Apr 9')\r\n\t\t// …\r\n\t\t// `month` is about 30.5 days.\r\n\t\tformatDatePastBy(month * 3).should.equal('Jan 10')\r\n\t\tformatDatePastBy(month * 4).should.equal('Dec 10, 2015')\r\n\t\tformatDatePastBy(year).should.equal('Apr 11, 2015')\r\n\r\n\t\t// Test future dates.\r\n\t\t// `month` is about 30.5 days.\r\n\t\tformatDatePastBy(-1 * month * 8).should.equal('Dec 10')\r\n\t\tformatDatePastBy(-1 * month * 9).should.equal('Jan 9, 2017')\r\n\t})\r\n\r\n\tit('should format Twitter style relative time (Russian)', () => {\r\n\t\tconst timeAgo = new TimeAgo('ru')\r\n\r\n\t\tconst now = new Date(2016, 3, 10, 22, 59).getTime()\r\n\t\tconst formatDatePastBy = (secondsPassed) => timeAgo.format(now - secondsPassed * 1000, { now, ...twitter })\r\n\r\n\t\tformatDatePastBy(0).should.equal('0 с')\r\n\t\tformatDatePastBy(1).should.equal('1 с')\r\n\t\tformatDatePastBy(minute).should.equal('1 мин')\r\n\t\tformatDatePastBy(hour).should.equal('1 ч')\r\n\t\tformatDatePastBy(day + 62 * minute).should.equal('9 апр.')\r\n\t\tformatDatePastBy(year).should.equal('11 апр. 2015 г.')\r\n\t})\r\n\r\n\tit('should format Twitter style relative time (Korean)', () => {\r\n\t\tconst timeAgo = new TimeAgo('ko')\r\n\r\n\t\tconst now = new Date(2016, 3, 10, 22, 59).getTime()\r\n\t\tconst formatDatePastBy = (secondsPassed) => timeAgo.format(now - secondsPassed * 1000, { now, ...twitter })\r\n\r\n\t\tformatDatePastBy(minute).should.equal('1분')\r\n\t\tformatDatePastBy(hour).should.equal('1시간')\r\n\t\tformatDatePastBy(day + 62 * minute).should.equal('4월 9일')\r\n\t\tformatDatePastBy(year).should.equal('2015년 4월 11일')\r\n\t})\r\n\r\n\tit('should format Twitter style relative time (German)', () => {\r\n\t\tconst timeAgo = new TimeAgo('de')\r\n\r\n\t\tconst now = new Date(2016, 3, 10, 22, 59).getTime()\r\n\t\tconst formatDatePastBy = (secondsPassed) => timeAgo.format(now - secondsPassed * 1000, { now, ...twitter })\r\n\r\n\t\tformatDatePastBy(minute).should.equal('1 Min.')\r\n\t\tformatDatePastBy(hour).should.equal('1 Std.')\r\n\t\tformatDatePastBy(day + 62 * minute).should.equal('9. Apr.')\r\n\t\tformatDatePastBy(year).should.equal('11. Apr. 2015')\r\n\t})\r\n\r\n\tit('should format Twitter style relative time (French)', () => {\r\n\t\tconst timeAgo = new TimeAgo('fr')\r\n\r\n\t\tconst now = new Date(2016, 3, 10, 22, 59).getTime()\r\n\t\tconst formatDatePastBy = (secondsPassed) => timeAgo.format(now - secondsPassed * 1000, { now, ...twitter })\r\n\r\n\t\tformatDatePastBy(minute).should.equal('1 min.')\r\n\t\tformatDatePastBy(hour).should.equal('1 h')\r\n\t\tformatDatePastBy(day + 62 * minute).should.equal('9 avr.')\r\n\t\tformatDatePastBy(year).should.equal('11 avr. 2015')\r\n\t})\r\n\r\n\tit('should format Twitter style relative time (Chinese)', () => {\r\n\t\tconst timeAgo = new TimeAgo('zh')\r\n\r\n\t\tconst now = new Date(2016, 3, 10, 22, 59).getTime()\r\n\t\tconst formatDatePastBy = (secondsPassed) => timeAgo.format(now - secondsPassed * 1000, { now, ...twitter })\r\n\r\n\t\tformatDatePastBy(minute).should.equal('1分钟')\r\n\t\tformatDatePastBy(hour).should.equal('1小时')\r\n\t\tformatDatePastBy(day + 62 * minute).should.equal('4月9日')\r\n\t\tformatDatePastBy(year).should.equal('2015年4月11日')\r\n\t})\r\n\r\n\t// This test won't pass because `Intl.DateTimeFormat` is read at\r\n\t// initialization time, not at run time.\r\n\t// it('should fall back to generic style when Intl.DateTimeFormat is not available', () => {\r\n\t// \tconst DateTimeFormat = Intl.DateTimeFormat\r\n\t// \tIntl.DateTimeFormat = undefined\r\n\t//\r\n\t// \tconst timeAgo = new TimeAgo('en')\r\n\t// \ttimeAgo.format(Date.now() - 365 * 24 * hour * 1000, 'twitter').should.equal('1yr')\r\n\t//\r\n\t// \tIntl.DateTimeFormat = DateTimeFormat\r\n\t// })\r\n\r\n\tit('should support timestamp argument on `yearMonthAndDay.test()`', () => {\r\n\t\tconst timeAgo = new TimeAgo('en')\r\n\t\ttimeAgo.format(0, 'twitter').should.equal('Jan 1, 1970')\r\n\t})\r\n\r\n\tit('should round as \"floor\"', () => {\r\n\t\tconst timeAgo = new TimeAgo('en')\r\n\t\tconst test = (time, result) => timeAgo.format(time, 'twitter', {\r\n\t\t\tround: 'floor',\r\n\t\t\tnow: 0\r\n\t\t}).should.equal(result)\r\n\t\ttest(2001, '2s')\r\n\t\ttest(2000, '2s')\r\n\t\ttest(1999, '1s')\r\n\t\ttest(1001, '1s')\r\n\t\ttest(1000, '1s')\r\n\t\ttest(999, '0s')\r\n\t\ttest(0, '0s')\r\n\t\ttest(-999, '0s')\r\n\t\ttest(-1000, '1s')\r\n\t\ttest(-1001, '1s')\r\n\t\ttest(-1999, '1s')\r\n\t\ttest(-2000, '2s')\r\n\t\ttest(-2001, '2s')\r\n\t})\r\n\r\n\tit('should get time to next update (round: \"floor\")', () => {\r\n\t\tconst timeAgo = new TimeAgo('en')\r\n\r\n\t\t// April 10th, 2018, 12:00.\r\n\t\tconst date = new Date(2018, 3, 10, 12, 0)\r\n\r\n\t\t// April 10th, 2016, 12:00 (two years earlier).\r\n\t\tlet now = new Date(2016, 3, 10, 12, 0).getTime()\r\n\r\n\t\ttimeAgo.format(\r\n\t\t\tdate,\r\n\t\t\t'twitter',\r\n\t\t\t{\r\n\t\t\t\tnow,\r\n\t\t\t\tgetTimeToNextUpdate: true\r\n\t\t\t}\r\n\t\t).should.deep.equal([\r\n\t\t\t'Apr 10, 2018',\r\n\t\t\t// Updates on Jan 1st, 2018, 00:00.\r\n\t\t\tnew Date(2018, 0, 1).getTime() - now\r\n\t\t])\r\n\r\n\t\t// 1st, 2018, 00:00.\r\n\t\tnow = new Date(2018, 0, 1).getTime()\r\n\r\n\t\ttimeAgo.format(\r\n\t\t\tdate,\r\n\t\t\t'twitter',\r\n\t\t\t{\r\n\t\t\t\tnow,\r\n\t\t\t\tgetTimeToNextUpdate: true,\r\n\t\t\t\tround: 'floor'\r\n\t\t\t}\r\n\t\t).should.deep.equal([\r\n\t\t\t'Apr 10',\r\n\t\t\t// Updates after April 9th, 2018, 12:00.\r\n\t\t\t(new Date(2018, 3, 9, 12, 0).getTime() + 1) - now\r\n\t\t])\r\n\r\n\t\t// After April 9th, 2018, 12:00.\r\n\t\tnow = new Date(2018, 3, 9, 12, 0).getTime() + 1\r\n\r\n\t\ttimeAgo.format(\r\n\t\t\tdate,\r\n\t\t\t'twitter',\r\n\t\t\t{\r\n\t\t\t\tnow,\r\n\t\t\t\tgetTimeToNextUpdate: true,\r\n\t\t\t\tround: 'floor'\r\n\t\t\t}\r\n\t\t).should.deep.equal([\r\n\t\t\t'23h',\r\n\t\t\t// Updates in an hour.\r\n\t\t\t60 * 60 * 1000\r\n\t\t])\r\n\t})\r\n\r\n\tit('should get time to next update (round: \"round\")', () => {\r\n\t\tconst timeAgo = new TimeAgo('en')\r\n\r\n\t\t// April 10th, 2018, 12:00.\r\n\t\tconst date = new Date(2018, 3, 10, 12, 0)\r\n\r\n\t\tlet now\r\n\r\n\t\t// 1st, 2018, 00:00.\r\n\t\tnow = new Date(2018, 0, 1).getTime()\r\n\r\n\t\ttimeAgo.format(\r\n\t\t\tdate,\r\n\t\t\t'twitter',\r\n\t\t\t{\r\n\t\t\t\tnow,\r\n\t\t\t\tgetTimeToNextUpdate: true\r\n\t\t\t}\r\n\t\t).should.deep.equal([\r\n\t\t\t'Apr 10',\r\n\t\t\t// Updates after April 9th, 2018, 11:30.\r\n\t\t\t(new Date(2018, 3, 9, 12, 0).getTime() + 30 * 60 * 1000 + 1) - now\r\n\t\t])\r\n\r\n\t\t// After April 9th, 2018, 12:00.\r\n\t\tnow = new Date(2018, 3, 9, 12, 0).getTime() + 30 * 60 * 1000 + 1\r\n\r\n\t\ttimeAgo.format(\r\n\t\t\tdate,\r\n\t\t\t'twitter',\r\n\t\t\t{\r\n\t\t\t\tnow,\r\n\t\t\t\tgetTimeToNextUpdate: true\r\n\t\t\t}\r\n\t\t).should.deep.equal([\r\n\t\t\t'23h',\r\n\t\t\t// Updates in an hour.\r\n\t\t\t60 * 60 * 1000\r\n\t\t])\r\n\t})\r\n})"], "mappings": ";;;;;;AAAA,OAAOA,OAAP,MAAoB,cAApB;AACA,OAAOC,OAAP,MAAoB,eAApB;AACA,SAASC,IAAT,EAAeC,MAAf,EAAuBC,GAAvB,EAA4BC,KAA5B,EAAmCC,IAAnC,QAA+C,mBAA/C;AAEAC,QAAQ,CAAC,eAAD,EAAkB,YAAM;EAC/BC,EAAE,CAAC,yCAAD,EAA4C,YAAM;IACnD,IAAMC,OAAO,GAAG,IAAIR,OAAJ,CAAY,KAAZ,CAAhB;IACAQ,OAAO,CAACC,MAAR,CAAeC,IAAI,CAACC,GAAL,KAAa,IAAIV,IAAJ,GAAW,IAAvC,EAA6C,SAA7C,EAAwDW,MAAxD,CAA+DC,OAA/D,CAAuE,oBAAvE;EACA,CAHC,CAAF;EAKAN,EAAE,CAAC,sEAAD,EAAyE,YAAM;IAChF,IAAMC,OAAO,GAAG,IAAIR,OAAJ,CAAY,IAAZ,CAAhB;;IAEA,IAAMc,gBAAgB,GAAG,SAAnBA,gBAAmB,CAACC,aAAD;MAAA,OAAmBP,OAAO,CAACC,MAAR,CAAe,CAACM,aAAD,GAAiB,IAAhC;QAAwCJ,GAAG,EAAE;MAA7C,GAAmDZ,OAAnD,EAAnB;IAAA,CAAzB;;IAEAe,gBAAgB,CAAC,IAAD,CAAhB,CAAuBF,MAAvB,CAA8BI,KAA9B,CAAoC,IAApC;IACAF,gBAAgB,CAAC,GAAD,CAAhB,CAAsBF,MAAtB,CAA6BI,KAA7B,CAAmC,IAAnC;IACAF,gBAAgB,CAAC,KAAD,CAAhB,CAAwBF,MAAxB,CAA+BI,KAA/B,CAAqC,KAArC;IACAF,gBAAgB,CAAC,IAAD,CAAhB,CAAuBF,MAAvB,CAA8BI,KAA9B,CAAoC,IAApC;IACAF,gBAAgB,CAAC,OAAOZ,MAAR,CAAhB,CAAgCU,MAAhC,CAAuCI,KAAvC,CAA6C,IAA7C;IACAF,gBAAgB,CAAC,MAAMZ,MAAP,CAAhB,CAA+BU,MAA/B,CAAsCI,KAAtC,CAA4C,IAA5C,EAVgF,CAWhF;;IACAF,gBAAgB,CAAC,QAAQZ,MAAT,CAAhB,CAAiCU,MAAjC,CAAwCI,KAAxC,CAA8C,KAA9C;IACAF,gBAAgB,CAAC,OAAOZ,MAAR,CAAhB,CAAgCU,MAAhC,CAAuCI,KAAvC,CAA6C,IAA7C;IACAF,gBAAgB,CAAC,OAAOb,IAAR,CAAhB,CAA8BW,MAA9B,CAAqCI,KAArC,CAA2C,IAA3C;IACAF,gBAAgB,CAAC,MAAMb,IAAP,CAAhB,CAA6BW,MAA7B,CAAoCI,KAApC,CAA0C,IAA1C,EAfgF,CAgBhF;;IACAF,gBAAgB,CAAC,QAAQb,IAAT,CAAhB,CAA+BW,MAA/B,CAAsCI,KAAtC,CAA4C,KAA5C;EACA,CAlBC,CAAF;EAoBAT,EAAE,CAAC,sEAAD,EAAyE,YAAM;IAChF,IAAMC,OAAO,GAAG,IAAIR,OAAJ,CAAY,IAAZ,CAAhB;;IAEA,IAAMc,gBAAgB,GAAG,SAAnBA,gBAAmB,CAACC,aAAD;MAAA,OAAmBP,OAAO,CAACC,MAAR,CAAe,CAACM,aAAD,GAAiB,IAAhC;QAAwCJ,GAAG,EAAE;MAA7C,GAAmDZ,OAAnD;QAA4DkB,KAAK,EAAE;MAAnE,GAAnB;IAAA,CAAzB;;IAEAH,gBAAgB,CAAC,CAAD,CAAhB,CAAoBF,MAApB,CAA2BI,KAA3B,CAAiC,IAAjC;IACAF,gBAAgB,CAAC,GAAD,CAAhB,CAAsBF,MAAtB,CAA6BI,KAA7B,CAAmC,IAAnC;IACAF,gBAAgB,CAAC,CAAD,CAAhB,CAAoBF,MAApB,CAA2BI,KAA3B,CAAiC,IAAjC;IACAF,gBAAgB,CAAC,IAAD,CAAhB,CAAuBF,MAAvB,CAA8BI,KAA9B,CAAoC,KAApC;IACAF,gBAAgB,CAAC,EAAD,CAAhB,CAAqBF,MAArB,CAA4BI,KAA5B,CAAkC,IAAlC;IACAF,gBAAgB,CAAC,MAAMZ,MAAP,CAAhB,CAA+BU,MAA/B,CAAsCI,KAAtC,CAA4C,IAA5C;IACAF,gBAAgB,CAAC,IAAIZ,MAAL,CAAhB,CAA6BU,MAA7B,CAAoCI,KAApC,CAA0C,IAA1C;IACAF,gBAAgB,CAAC,MAAMZ,MAAP,CAAhB,CAA+BU,MAA/B,CAAsCI,KAAtC,CAA4C,IAA5C;IACAF,gBAAgB,CAAC,IAAIZ,MAAL,CAAhB,CAA6BU,MAA7B,CAAoCI,KAApC,CAA0C,IAA1C,EAbgF,CAchF;;IACAF,gBAAgB,CAAC,OAAOZ,MAAR,CAAhB,CAAgCU,MAAhC,CAAuCI,KAAvC,CAA6C,KAA7C;IACAF,gBAAgB,CAAC,KAAKZ,MAAN,CAAhB,CAA8BU,MAA9B,CAAqCI,KAArC,CAA2C,IAA3C;IACAF,gBAAgB,CAAC,MAAMb,IAAP,CAAhB,CAA6BW,MAA7B,CAAoCI,KAApC,CAA0C,IAA1C;IACAF,gBAAgB,CAAC,IAAIb,IAAL,CAAhB,CAA2BW,MAA3B,CAAkCI,KAAlC,CAAwC,IAAxC;IACAF,gBAAgB,CAAC,MAAMb,IAAP,CAAhB,CAA6BW,MAA7B,CAAoCI,KAApC,CAA0C,IAA1C;IACAF,gBAAgB,CAAC,IAAIb,IAAL,CAAhB,CAA2BW,MAA3B,CAAkCI,KAAlC,CAAwC,IAAxC,EApBgF,CAqBhF;;IACAF,gBAAgB,CAAC,OAAOb,IAAR,CAAhB,CAA8BW,MAA9B,CAAqCI,KAArC,CAA2C,KAA3C;EACA,CAvBC,CAAF;EAyBAT,EAAE,CAAC,sEAAD,EAAyE,YAAM;IAChF,IAAMC,OAAO,GAAG,IAAIR,OAAJ,CAAY,IAAZ,CAAhB,CADgF,CAGhF;;IACA,IAAMW,GAAG,GAAG,IAAID,IAAJ,CAAS,IAAT,EAAe,CAAf,EAAkB,EAAlB,EAAsB,EAAtB,EAA0B,CAA1B,EAA6BQ,OAA7B,EAAZ;;IACA,IAAMJ,gBAAgB,GAAG,SAAnBA,gBAAmB,CAACC,aAAD;MAAA,OAAmBP,OAAO,CAACC,MAAR,CAAeE,GAAG,GAAGI,aAAa,GAAG,IAArC;QAA6CJ,GAAG,EAAHA;MAA7C,GAAqDZ,OAArD,EAAnB;IAAA,CAAzB;;IAEAe,gBAAgB,CAACX,GAAG,GAAG,IAAID,MAAV,GAAmBD,IAApB,CAAhB,CAA0CW,MAA1C,CAAiDI,KAAjD,CAAuD,OAAvD,EAPgF,CAQhF;IACA;;IACAF,gBAAgB,CAACV,KAAK,GAAG,CAAT,CAAhB,CAA4BQ,MAA5B,CAAmCI,KAAnC,CAAyC,QAAzC;IACAF,gBAAgB,CAACV,KAAK,GAAG,CAAT,CAAhB,CAA4BQ,MAA5B,CAAmCI,KAAnC,CAAyC,cAAzC;IACAF,gBAAgB,CAACT,IAAD,CAAhB,CAAuBO,MAAvB,CAA8BI,KAA9B,CAAoC,cAApC,EAZgF,CAchF;IACA;;IACAF,gBAAgB,CAAC,CAAC,CAAD,GAAKV,KAAL,GAAa,CAAd,CAAhB,CAAiCQ,MAAjC,CAAwCI,KAAxC,CAA8C,QAA9C;IACAF,gBAAgB,CAAC,CAAC,CAAD,GAAKV,KAAL,GAAa,CAAd,CAAhB,CAAiCQ,MAAjC,CAAwCI,KAAxC,CAA8C,aAA9C;EACA,CAlBC,CAAF;EAoBAT,EAAE,CAAC,qDAAD,EAAwD,YAAM;IAC/D,IAAMC,OAAO,GAAG,IAAIR,OAAJ,CAAY,IAAZ,CAAhB;IAEA,IAAMW,GAAG,GAAG,IAAID,IAAJ,CAAS,IAAT,EAAe,CAAf,EAAkB,EAAlB,EAAsB,EAAtB,EAA0B,EAA1B,EAA8BQ,OAA9B,EAAZ;;IACA,IAAMJ,gBAAgB,GAAG,SAAnBA,gBAAmB,CAACC,aAAD;MAAA,OAAmBP,OAAO,CAACC,MAAR,CAAeE,GAAG,GAAGI,aAAa,GAAG,IAArC;QAA6CJ,GAAG,EAAHA;MAA7C,GAAqDZ,OAArD,EAAnB;IAAA,CAAzB;;IAEAe,gBAAgB,CAAC,CAAD,CAAhB,CAAoBF,MAApB,CAA2BI,KAA3B,CAAiC,KAAjC;IACAF,gBAAgB,CAAC,CAAD,CAAhB,CAAoBF,MAApB,CAA2BI,KAA3B,CAAiC,KAAjC;IACAF,gBAAgB,CAACZ,MAAD,CAAhB,CAAyBU,MAAzB,CAAgCI,KAAhC,CAAsC,OAAtC;IACAF,gBAAgB,CAACb,IAAD,CAAhB,CAAuBW,MAAvB,CAA8BI,KAA9B,CAAoC,KAApC;IACAF,gBAAgB,CAACX,GAAG,GAAG,KAAKD,MAAZ,CAAhB,CAAoCU,MAApC,CAA2CI,KAA3C,CAAiD,QAAjD;IACAF,gBAAgB,CAACT,IAAD,CAAhB,CAAuBO,MAAvB,CAA8BI,KAA9B,CAAoC,iBAApC;EACA,CAZC,CAAF;EAcAT,EAAE,CAAC,oDAAD,EAAuD,YAAM;IAC9D,IAAMC,OAAO,GAAG,IAAIR,OAAJ,CAAY,IAAZ,CAAhB;IAEA,IAAMW,GAAG,GAAG,IAAID,IAAJ,CAAS,IAAT,EAAe,CAAf,EAAkB,EAAlB,EAAsB,EAAtB,EAA0B,EAA1B,EAA8BQ,OAA9B,EAAZ;;IACA,IAAMJ,gBAAgB,GAAG,SAAnBA,gBAAmB,CAACC,aAAD;MAAA,OAAmBP,OAAO,CAACC,MAAR,CAAeE,GAAG,GAAGI,aAAa,GAAG,IAArC;QAA6CJ,GAAG,EAAHA;MAA7C,GAAqDZ,OAArD,EAAnB;IAAA,CAAzB;;IAEAe,gBAAgB,CAACZ,MAAD,CAAhB,CAAyBU,MAAzB,CAAgCI,KAAhC,CAAsC,IAAtC;IACAF,gBAAgB,CAACb,IAAD,CAAhB,CAAuBW,MAAvB,CAA8BI,KAA9B,CAAoC,KAApC;IACAF,gBAAgB,CAACX,GAAG,GAAG,KAAKD,MAAZ,CAAhB,CAAoCU,MAApC,CAA2CI,KAA3C,CAAiD,OAAjD;IACAF,gBAAgB,CAACT,IAAD,CAAhB,CAAuBO,MAAvB,CAA8BI,KAA9B,CAAoC,cAApC;EACA,CAVC,CAAF;EAYAT,EAAE,CAAC,oDAAD,EAAuD,YAAM;IAC9D,IAAMC,OAAO,GAAG,IAAIR,OAAJ,CAAY,IAAZ,CAAhB;IAEA,IAAMW,GAAG,GAAG,IAAID,IAAJ,CAAS,IAAT,EAAe,CAAf,EAAkB,EAAlB,EAAsB,EAAtB,EAA0B,EAA1B,EAA8BQ,OAA9B,EAAZ;;IACA,IAAMJ,gBAAgB,GAAG,SAAnBA,gBAAmB,CAACC,aAAD;MAAA,OAAmBP,OAAO,CAACC,MAAR,CAAeE,GAAG,GAAGI,aAAa,GAAG,IAArC;QAA6CJ,GAAG,EAAHA;MAA7C,GAAqDZ,OAArD,EAAnB;IAAA,CAAzB;;IAEAe,gBAAgB,CAACZ,MAAD,CAAhB,CAAyBU,MAAzB,CAAgCI,KAAhC,CAAsC,QAAtC;IACAF,gBAAgB,CAACb,IAAD,CAAhB,CAAuBW,MAAvB,CAA8BI,KAA9B,CAAoC,QAApC;IACAF,gBAAgB,CAACX,GAAG,GAAG,KAAKD,MAAZ,CAAhB,CAAoCU,MAApC,CAA2CI,KAA3C,CAAiD,SAAjD;IACAF,gBAAgB,CAACT,IAAD,CAAhB,CAAuBO,MAAvB,CAA8BI,KAA9B,CAAoC,eAApC;EACA,CAVC,CAAF;EAYAT,EAAE,CAAC,oDAAD,EAAuD,YAAM;IAC9D,IAAMC,OAAO,GAAG,IAAIR,OAAJ,CAAY,IAAZ,CAAhB;IAEA,IAAMW,GAAG,GAAG,IAAID,IAAJ,CAAS,IAAT,EAAe,CAAf,EAAkB,EAAlB,EAAsB,EAAtB,EAA0B,EAA1B,EAA8BQ,OAA9B,EAAZ;;IACA,IAAMJ,gBAAgB,GAAG,SAAnBA,gBAAmB,CAACC,aAAD;MAAA,OAAmBP,OAAO,CAACC,MAAR,CAAeE,GAAG,GAAGI,aAAa,GAAG,IAArC;QAA6CJ,GAAG,EAAHA;MAA7C,GAAqDZ,OAArD,EAAnB;IAAA,CAAzB;;IAEAe,gBAAgB,CAACZ,MAAD,CAAhB,CAAyBU,MAAzB,CAAgCI,KAAhC,CAAsC,QAAtC;IACAF,gBAAgB,CAACb,IAAD,CAAhB,CAAuBW,MAAvB,CAA8BI,KAA9B,CAAoC,KAApC;IACAF,gBAAgB,CAACX,GAAG,GAAG,KAAKD,MAAZ,CAAhB,CAAoCU,MAApC,CAA2CI,KAA3C,CAAiD,QAAjD;IACAF,gBAAgB,CAACT,IAAD,CAAhB,CAAuBO,MAAvB,CAA8BI,KAA9B,CAAoC,cAApC;EACA,CAVC,CAAF;EAYAT,EAAE,CAAC,qDAAD,EAAwD,YAAM;IAC/D,IAAMC,OAAO,GAAG,IAAIR,OAAJ,CAAY,IAAZ,CAAhB;IAEA,IAAMW,GAAG,GAAG,IAAID,IAAJ,CAAS,IAAT,EAAe,CAAf,EAAkB,EAAlB,EAAsB,EAAtB,EAA0B,EAA1B,EAA8BQ,OAA9B,EAAZ;;IACA,IAAMJ,gBAAgB,GAAG,SAAnBA,gBAAmB,CAACC,aAAD;MAAA,OAAmBP,OAAO,CAACC,MAAR,CAAeE,GAAG,GAAGI,aAAa,GAAG,IAArC;QAA6CJ,GAAG,EAAHA;MAA7C,GAAqDZ,OAArD,EAAnB;IAAA,CAAzB;;IAEAe,gBAAgB,CAACZ,MAAD,CAAhB,CAAyBU,MAAzB,CAAgCI,KAAhC,CAAsC,KAAtC;IACAF,gBAAgB,CAACb,IAAD,CAAhB,CAAuBW,MAAvB,CAA8BI,KAA9B,CAAoC,KAApC;IACAF,gBAAgB,CAACX,GAAG,GAAG,KAAKD,MAAZ,CAAhB,CAAoCU,MAApC,CAA2CI,KAA3C,CAAiD,MAAjD;IACAF,gBAAgB,CAACT,IAAD,CAAhB,CAAuBO,MAAvB,CAA8BI,KAA9B,CAAoC,YAApC;EACA,CAVC,CAAF,CAzH+B,CAqI/B;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EAEAT,EAAE,CAAC,+DAAD,EAAkE,YAAM;IACzE,IAAMC,OAAO,GAAG,IAAIR,OAAJ,CAAY,IAAZ,CAAhB;IACAQ,OAAO,CAACC,MAAR,CAAe,CAAf,EAAkB,SAAlB,EAA6BG,MAA7B,CAAoCI,KAApC,CAA0C,aAA1C;EACA,CAHC,CAAF;EAKAT,EAAE,CAAC,yBAAD,EAA4B,YAAM;IACnC,IAAMC,OAAO,GAAG,IAAIR,OAAJ,CAAY,IAAZ,CAAhB;;IACA,IAAMmB,IAAI,GAAG,SAAPA,IAAO,CAACC,IAAD,EAAOC,MAAP;MAAA,OAAkBb,OAAO,CAACC,MAAR,CAAeW,IAAf,EAAqB,SAArB,EAAgC;QAC9DH,KAAK,EAAE,OADuD;QAE9DN,GAAG,EAAE;MAFyD,CAAhC,EAG5BC,MAH4B,CAGrBI,KAHqB,CAGfK,MAHe,CAAlB;IAAA,CAAb;;IAIAF,IAAI,CAAC,IAAD,EAAO,IAAP,CAAJ;IACAA,IAAI,CAAC,IAAD,EAAO,IAAP,CAAJ;IACAA,IAAI,CAAC,IAAD,EAAO,IAAP,CAAJ;IACAA,IAAI,CAAC,IAAD,EAAO,IAAP,CAAJ;IACAA,IAAI,CAAC,IAAD,EAAO,IAAP,CAAJ;IACAA,IAAI,CAAC,GAAD,EAAM,IAAN,CAAJ;IACAA,IAAI,CAAC,CAAD,EAAI,IAAJ,CAAJ;IACAA,IAAI,CAAC,CAAC,GAAF,EAAO,IAAP,CAAJ;IACAA,IAAI,CAAC,CAAC,IAAF,EAAQ,IAAR,CAAJ;IACAA,IAAI,CAAC,CAAC,IAAF,EAAQ,IAAR,CAAJ;IACAA,IAAI,CAAC,CAAC,IAAF,EAAQ,IAAR,CAAJ;IACAA,IAAI,CAAC,CAAC,IAAF,EAAQ,IAAR,CAAJ;IACAA,IAAI,CAAC,CAAC,IAAF,EAAQ,IAAR,CAAJ;EACA,CAnBC,CAAF;EAqBAZ,EAAE,CAAC,iDAAD,EAAoD,YAAM;IAC3D,IAAMC,OAAO,GAAG,IAAIR,OAAJ,CAAY,IAAZ,CAAhB,CAD2D,CAG3D;;IACA,IAAMsB,IAAI,GAAG,IAAIZ,IAAJ,CAAS,IAAT,EAAe,CAAf,EAAkB,EAAlB,EAAsB,EAAtB,EAA0B,CAA1B,CAAb,CAJ2D,CAM3D;;IACA,IAAIC,GAAG,GAAG,IAAID,IAAJ,CAAS,IAAT,EAAe,CAAf,EAAkB,EAAlB,EAAsB,EAAtB,EAA0B,CAA1B,EAA6BQ,OAA7B,EAAV;IAEAV,OAAO,CAACC,MAAR,CACCa,IADD,EAEC,SAFD,EAGC;MACCX,GAAG,EAAHA,GADD;MAECY,mBAAmB,EAAE;IAFtB,CAHD,EAOEX,MAPF,CAOSY,IAPT,CAOcR,KAPd,CAOoB,CACnB,cADmB,EAEnB;IACA,IAAIN,IAAJ,CAAS,IAAT,EAAe,CAAf,EAAkB,CAAlB,EAAqBQ,OAArB,KAAiCP,GAHd,CAPpB,EAT2D,CAsB3D;;IACAA,GAAG,GAAG,IAAID,IAAJ,CAAS,IAAT,EAAe,CAAf,EAAkB,CAAlB,EAAqBQ,OAArB,EAAN;IAEAV,OAAO,CAACC,MAAR,CACCa,IADD,EAEC,SAFD,EAGC;MACCX,GAAG,EAAHA,GADD;MAECY,mBAAmB,EAAE,IAFtB;MAGCN,KAAK,EAAE;IAHR,CAHD,EAQEL,MARF,CAQSY,IART,CAQcR,KARd,CAQoB,CACnB,QADmB,EAEnB;IACC,IAAIN,IAAJ,CAAS,IAAT,EAAe,CAAf,EAAkB,CAAlB,EAAqB,EAArB,EAAyB,CAAzB,EAA4BQ,OAA5B,KAAwC,CAAzC,GAA8CP,GAH3B,CARpB,EAzB2D,CAuC3D;;IACAA,GAAG,GAAG,IAAID,IAAJ,CAAS,IAAT,EAAe,CAAf,EAAkB,CAAlB,EAAqB,EAArB,EAAyB,CAAzB,EAA4BQ,OAA5B,KAAwC,CAA9C;IAEAV,OAAO,CAACC,MAAR,CACCa,IADD,EAEC,SAFD,EAGC;MACCX,GAAG,EAAHA,GADD;MAECY,mBAAmB,EAAE,IAFtB;MAGCN,KAAK,EAAE;IAHR,CAHD,EAQEL,MARF,CAQSY,IART,CAQcR,KARd,CAQoB,CACnB,KADmB,EAEnB;IACA,KAAK,EAAL,GAAU,IAHS,CARpB;EAaA,CAvDC,CAAF;EAyDAT,EAAE,CAAC,iDAAD,EAAoD,YAAM;IAC3D,IAAMC,OAAO,GAAG,IAAIR,OAAJ,CAAY,IAAZ,CAAhB,CAD2D,CAG3D;;IACA,IAAMsB,IAAI,GAAG,IAAIZ,IAAJ,CAAS,IAAT,EAAe,CAAf,EAAkB,EAAlB,EAAsB,EAAtB,EAA0B,CAA1B,CAAb;IAEA,IAAIC,GAAJ,CAN2D,CAQ3D;;IACAA,GAAG,GAAG,IAAID,IAAJ,CAAS,IAAT,EAAe,CAAf,EAAkB,CAAlB,EAAqBQ,OAArB,EAAN;IAEAV,OAAO,CAACC,MAAR,CACCa,IADD,EAEC,SAFD,EAGC;MACCX,GAAG,EAAHA,GADD;MAECY,mBAAmB,EAAE;IAFtB,CAHD,EAOEX,MAPF,CAOSY,IAPT,CAOcR,KAPd,CAOoB,CACnB,QADmB,EAEnB;IACC,IAAIN,IAAJ,CAAS,IAAT,EAAe,CAAf,EAAkB,CAAlB,EAAqB,EAArB,EAAyB,CAAzB,EAA4BQ,OAA5B,KAAwC,KAAK,EAAL,GAAU,IAAlD,GAAyD,CAA1D,GAA+DP,GAH5C,CAPpB,EAX2D,CAwB3D;;IACAA,GAAG,GAAG,IAAID,IAAJ,CAAS,IAAT,EAAe,CAAf,EAAkB,CAAlB,EAAqB,EAArB,EAAyB,CAAzB,EAA4BQ,OAA5B,KAAwC,KAAK,EAAL,GAAU,IAAlD,GAAyD,CAA/D;IAEAV,OAAO,CAACC,MAAR,CACCa,IADD,EAEC,SAFD,EAGC;MACCX,GAAG,EAAHA,GADD;MAECY,mBAAmB,EAAE;IAFtB,CAHD,EAOEX,MAPF,CAOSY,IAPT,CAOcR,KAPd,CAOoB,CACnB,KADmB,EAEnB;IACA,KAAK,EAAL,GAAU,IAHS,CAPpB;EAYA,CAvCC,CAAF;AAwCA,CA5QO,CAAR"}