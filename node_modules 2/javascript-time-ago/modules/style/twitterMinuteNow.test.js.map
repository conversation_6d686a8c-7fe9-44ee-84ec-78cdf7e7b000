{"version": 3, "file": "twitterMinuteNow.test.js", "names": ["twitter", "TimeAgo", "hour", "minute", "day", "month", "year", "describe", "it", "timeAgo", "now", "Date", "getTime", "formatInterval", "secondsPassed", "format", "round", "should", "equal"], "sources": ["../../source/style/twitterMinuteNow.test.js"], "sourcesContent": ["import twitter from './twitterMinuteNow.js'\r\nimport TimeAgo from '../TimeAgo.js'\r\nimport { hour, minute, day, month, year } from '../steps/index.js'\r\n\r\ndescribe('style/twitterNow', () => {\r\n\tit('should format Twitter style relative time (English) (round: \"floor\")', () => {\r\n\t\tconst timeAgo = new TimeAgo('en')\r\n\r\n\t\t// April 10th, 2016.\r\n\t\tconst now = new Date(2016, 3, 10, 22, 59).getTime()\r\n\t\tconst formatInterval = (secondsPassed) => timeAgo.format(now - secondsPassed * 1000, { now, ...twitter, round: 'floor' })\r\n\r\n\t\tformatInterval(0).should.equal('now')\r\n\t\tformatInterval(59.9).should.equal('now')\r\n\t\tformatInterval(60).should.equal('1m')\r\n\t\tformatInterval(1.9 * minute).should.equal('1m')\r\n\t\tformatInterval(2 * minute).should.equal('2m')\r\n\t\tformatInterval(2.9 * minute).should.equal('2m')\r\n\t\tformatInterval(3 * minute).should.equal('3m')\r\n\t\t// …\r\n\t\tformatInterval(59.9 * minute).should.equal('59m')\r\n\t\tformatInterval(60 * minute).should.equal('1h')\r\n\t\tformatInterval(1.9 * hour).should.equal('1h')\r\n\t\tformatInterval(2 * hour).should.equal('2h')\r\n\t\tformatInterval(2.9 * hour).should.equal('2h')\r\n\t\tformatInterval(3 * hour).should.equal('3h')\r\n\t\t// …\r\n\t\tformatInterval(23.9 * hour).should.equal('23h')\r\n\t\tformatInterval(day + 2 * minute + hour).should.equal('Apr 9')\r\n\t\t// …\r\n\t\t// `month` is about 30.5 days.\r\n\t\tformatInterval(month * 3).should.equal('Jan 10')\r\n\t\tformatInterval(month * 4).should.equal('Dec 11, 2015')\r\n\t\tformatInterval(year).should.equal('Apr 11, 2015')\r\n\r\n\t\t// Test future dates.\r\n\t\t// `month` is about 30.5 days.\r\n\t\tformatInterval(-1 * month * 8).should.equal('Dec 10')\r\n\t\tformatInterval(-1 * month * 9).should.equal('Jan 9, 2017')\r\n\t})\r\n\r\n\tit('should format Twitter style relative time (English) (round: \"round\")', () => {\r\n\t\tconst timeAgo = new TimeAgo('en')\r\n\r\n\t\t// April 10th, 2016.\r\n\t\tconst now = new Date(2016, 3, 10, 22, 59).getTime()\r\n\t\tconst formatInterval = (secondsPassed) => timeAgo.format(now - secondsPassed * 1000, { now, ...twitter, round: 'round' })\r\n\r\n\t\tformatInterval(0).should.equal('now')\r\n\t\tformatInterval(29.9).should.equal('now')\r\n\t\tformatInterval(30).should.equal('1m')\r\n\t\tformatInterval(1.49 * minute).should.equal('1m')\r\n\t\tformatInterval(1.5 * minute).should.equal('2m')\r\n\t\tformatInterval(2.49 * minute).should.equal('2m')\r\n\t\tformatInterval(2.5 * minute).should.equal('3m')\r\n\t})\r\n})"], "mappings": ";;;;;;AAAA,OAAOA,OAAP,MAAoB,uBAApB;AACA,OAAOC,OAAP,MAAoB,eAApB;AACA,SAASC,IAAT,EAAeC,MAAf,EAAuBC,GAAvB,EAA4BC,KAA5B,EAAmCC,IAAnC,QAA+C,mBAA/C;AAEAC,QAAQ,CAAC,kBAAD,EAAqB,YAAM;EAClCC,EAAE,CAAC,sEAAD,EAAyE,YAAM;IAChF,IAAMC,OAAO,GAAG,IAAIR,OAAJ,CAAY,IAAZ,CAAhB,CADgF,CAGhF;;IACA,IAAMS,GAAG,GAAG,IAAIC,IAAJ,CAAS,IAAT,EAAe,CAAf,EAAkB,EAAlB,EAAsB,EAAtB,EAA0B,EAA1B,EAA8BC,OAA9B,EAAZ;;IACA,IAAMC,cAAc,GAAG,SAAjBA,cAAiB,CAACC,aAAD;MAAA,OAAmBL,OAAO,CAACM,MAAR,CAAeL,GAAG,GAAGI,aAAa,GAAG,IAArC;QAA6CJ,GAAG,EAAHA;MAA7C,GAAqDV,OAArD;QAA8DgB,KAAK,EAAE;MAArE,GAAnB;IAAA,CAAvB;;IAEAH,cAAc,CAAC,CAAD,CAAd,CAAkBI,MAAlB,CAAyBC,KAAzB,CAA+B,KAA/B;IACAL,cAAc,CAAC,IAAD,CAAd,CAAqBI,MAArB,CAA4BC,KAA5B,CAAkC,KAAlC;IACAL,cAAc,CAAC,EAAD,CAAd,CAAmBI,MAAnB,CAA0BC,KAA1B,CAAgC,IAAhC;IACAL,cAAc,CAAC,MAAMV,MAAP,CAAd,CAA6Bc,MAA7B,CAAoCC,KAApC,CAA0C,IAA1C;IACAL,cAAc,CAAC,IAAIV,MAAL,CAAd,CAA2Bc,MAA3B,CAAkCC,KAAlC,CAAwC,IAAxC;IACAL,cAAc,CAAC,MAAMV,MAAP,CAAd,CAA6Bc,MAA7B,CAAoCC,KAApC,CAA0C,IAA1C;IACAL,cAAc,CAAC,IAAIV,MAAL,CAAd,CAA2Bc,MAA3B,CAAkCC,KAAlC,CAAwC,IAAxC,EAbgF,CAchF;;IACAL,cAAc,CAAC,OAAOV,MAAR,CAAd,CAA8Bc,MAA9B,CAAqCC,KAArC,CAA2C,KAA3C;IACAL,cAAc,CAAC,KAAKV,MAAN,CAAd,CAA4Bc,MAA5B,CAAmCC,KAAnC,CAAyC,IAAzC;IACAL,cAAc,CAAC,MAAMX,IAAP,CAAd,CAA2Be,MAA3B,CAAkCC,KAAlC,CAAwC,IAAxC;IACAL,cAAc,CAAC,IAAIX,IAAL,CAAd,CAAyBe,MAAzB,CAAgCC,KAAhC,CAAsC,IAAtC;IACAL,cAAc,CAAC,MAAMX,IAAP,CAAd,CAA2Be,MAA3B,CAAkCC,KAAlC,CAAwC,IAAxC;IACAL,cAAc,CAAC,IAAIX,IAAL,CAAd,CAAyBe,MAAzB,CAAgCC,KAAhC,CAAsC,IAAtC,EApBgF,CAqBhF;;IACAL,cAAc,CAAC,OAAOX,IAAR,CAAd,CAA4Be,MAA5B,CAAmCC,KAAnC,CAAyC,KAAzC;IACAL,cAAc,CAACT,GAAG,GAAG,IAAID,MAAV,GAAmBD,IAApB,CAAd,CAAwCe,MAAxC,CAA+CC,KAA/C,CAAqD,OAArD,EAvBgF,CAwBhF;IACA;;IACAL,cAAc,CAACR,KAAK,GAAG,CAAT,CAAd,CAA0BY,MAA1B,CAAiCC,KAAjC,CAAuC,QAAvC;IACAL,cAAc,CAACR,KAAK,GAAG,CAAT,CAAd,CAA0BY,MAA1B,CAAiCC,KAAjC,CAAuC,cAAvC;IACAL,cAAc,CAACP,IAAD,CAAd,CAAqBW,MAArB,CAA4BC,KAA5B,CAAkC,cAAlC,EA5BgF,CA8BhF;IACA;;IACAL,cAAc,CAAC,CAAC,CAAD,GAAKR,KAAL,GAAa,CAAd,CAAd,CAA+BY,MAA/B,CAAsCC,KAAtC,CAA4C,QAA5C;IACAL,cAAc,CAAC,CAAC,CAAD,GAAKR,KAAL,GAAa,CAAd,CAAd,CAA+BY,MAA/B,CAAsCC,KAAtC,CAA4C,aAA5C;EACA,CAlCC,CAAF;EAoCAV,EAAE,CAAC,sEAAD,EAAyE,YAAM;IAChF,IAAMC,OAAO,GAAG,IAAIR,OAAJ,CAAY,IAAZ,CAAhB,CADgF,CAGhF;;IACA,IAAMS,GAAG,GAAG,IAAIC,IAAJ,CAAS,IAAT,EAAe,CAAf,EAAkB,EAAlB,EAAsB,EAAtB,EAA0B,EAA1B,EAA8BC,OAA9B,EAAZ;;IACA,IAAMC,cAAc,GAAG,SAAjBA,cAAiB,CAACC,aAAD;MAAA,OAAmBL,OAAO,CAACM,MAAR,CAAeL,GAAG,GAAGI,aAAa,GAAG,IAArC;QAA6CJ,GAAG,EAAHA;MAA7C,GAAqDV,OAArD;QAA8DgB,KAAK,EAAE;MAArE,GAAnB;IAAA,CAAvB;;IAEAH,cAAc,CAAC,CAAD,CAAd,CAAkBI,MAAlB,CAAyBC,KAAzB,CAA+B,KAA/B;IACAL,cAAc,CAAC,IAAD,CAAd,CAAqBI,MAArB,CAA4BC,KAA5B,CAAkC,KAAlC;IACAL,cAAc,CAAC,EAAD,CAAd,CAAmBI,MAAnB,CAA0BC,KAA1B,CAAgC,IAAhC;IACAL,cAAc,CAAC,OAAOV,MAAR,CAAd,CAA8Bc,MAA9B,CAAqCC,KAArC,CAA2C,IAA3C;IACAL,cAAc,CAAC,MAAMV,MAAP,CAAd,CAA6Bc,MAA7B,CAAoCC,KAApC,CAA0C,IAA1C;IACAL,cAAc,CAAC,OAAOV,MAAR,CAAd,CAA8Bc,MAA9B,CAAqCC,KAArC,CAA2C,IAA3C;IACAL,cAAc,CAAC,MAAMV,MAAP,CAAd,CAA6Bc,MAA7B,CAAoCC,KAApC,CAA0C,IAA1C;EACA,CAdC,CAAF;AAeA,CApDO,CAAR"}