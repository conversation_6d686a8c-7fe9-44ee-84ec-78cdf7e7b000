{"version": 3, "file": "twitter.js", "names": ["day", "getDate", "intlDateTimeFormatSupported", "renameLegacyProperties", "steps", "formatAs", "formatters", "monthAndDay", "minTime", "timestamp", "future", "getMinTimeForUnit", "format", "value", "locale", "day<PERSON><PERSON><PERSON>", "Intl", "DateTimeFormat", "month", "yearMonthAndDay", "maxFittingNow", "Date", "getFullYear", "getTime", "minFittingNow", "dayMonthYear", "year", "push", "labels"], "sources": ["../../source/style/twitter.js"], "sourcesContent": ["import { day, getDate } from '../steps/index.js'\r\nimport { intlDateTimeFormatSupported } from '../locale.js'\r\n\r\n// For compatibility with the old versions of this library.\r\nimport renameLegacyProperties from './renameLegacyProperties.js'\r\n\r\n// Twitter-style relative date/time formatting.\r\n// (\"1m\", \"2h\", \"Mar 3\", \"Apr 4, 2012\").\r\n//\r\n// Seconds, minutes or hours are shown for shorter intervals,\r\n// and longer intervals are formatted using full date format.\r\n\r\nconst steps = [\r\n\t{\r\n\t\tformatAs: 'second'\r\n\t},\r\n\t{\r\n\t\tformatAs: 'minute'\r\n\t},\r\n\t{\r\n\t\tformatAs: 'hour'\r\n\t}\r\n]\r\n\r\n// A cache for `Intl.DateTimeFormat` formatters\r\n// for various locales (is a global variable).\r\nconst formatters = {}\r\n\r\n// Starting from day intervals, output month and day.\r\nconst monthAndDay = {\r\n\tminTime(timestamp, { future, getMinTimeForUnit }) {\r\n\t\t// Returns `23.5 * 60 * 60` when `round` is \"round\",\r\n\t\t// and `24 * 60 * 60` when `round` is \"floor\".\r\n\t\treturn getMinTimeForUnit('day')\r\n\t},\r\n\tformat(value, locale) {\r\n\t\t/* istanbul ignore else */\r\n\t\tif (!formatters[locale]) {\r\n\t\t\tformatters[locale] = {}\r\n\t\t}\r\n\t\t/* istanbul ignore else */\r\n\t\tif (!formatters[locale].dayMonth) {\r\n\t\t\t// \"Apr 11\" (MMMd)\r\n\t\t\tformatters[locale].dayMonth = new Intl.DateTimeFormat(locale, {\r\n\t\t\t\tmonth: 'short',\r\n\t\t\t\tday: 'numeric'\r\n\t\t\t})\r\n\t\t}\r\n\t\t// Output month and day.\r\n\t\treturn formatters[locale].dayMonth.format(getDate(value))\r\n\t}\r\n}\r\n\r\n// If the `date` happened/happens outside of current year,\r\n// then output day, month and year.\r\n// The interval should be such that the `date` lies outside of the current year.\r\nconst yearMonthAndDay = {\r\n\tminTime(timestamp, { future }) {\r\n\t\tif (future) {\r\n\t\t\t// January 1, 00:00, of the `date`'s year is right after\r\n\t\t\t// the maximum `now` for formatting a future date:\r\n\t\t\t// When `now` is before that date, the `date` is formatted as \"day/month/year\" (this step),\r\n\t\t\t// When `now` is equal to or after that date, the `date` is formatted as \"day/month\" (another step).\r\n\t\t\t// After that, it's hours, minutes, seconds, and after that it's no longer `future`.\r\n\t\t\t// The date is right after the maximum `now` for formatting a future date,\r\n\t\t\t// so subtract 1 millisecond from it.\r\n\t\t\tconst maxFittingNow = new Date(new Date(timestamp).getFullYear(), 0).getTime() - 1\r\n\t\t\t// Return `minTime` (in seconds).\r\n\t\t\treturn (timestamp - maxFittingNow) / 1000\r\n\t\t} else {\r\n\t\t\t// January 1, 00:00, of the year following the `date`'s year\r\n\t\t\t// is the minimum `now` for formatting a past date:\r\n\t\t\t// When `now` is before that date, the `date` is formatted as \"day/month\" (another step),\r\n\t\t\t// When `now` is equal to or after that date, the `date` is formatted as \"day/month/year\" (this step).\r\n\t\t\t// After that, it's hours, minutes, seconds, and after that it's no longer `future`.\r\n\t\t\tconst minFittingNow = new Date(new Date(timestamp).getFullYear() + 1, 0).getTime()\r\n\t\t\t// Return `minTime` (in seconds).\r\n\t\t\treturn (minFittingNow - timestamp) / 1000\r\n\t\t}\r\n\t},\r\n\tformat(value, locale) {\r\n\t\t/* istanbul ignore if */\r\n\t\tif (!formatters[locale]) {\r\n\t\t\tformatters[locale] = {}\r\n\t\t}\r\n\t\t/* istanbul ignore else */\r\n\t\tif (!formatters[locale].dayMonthYear) {\r\n\t\t\t// \"Apr 11, 2017\" (yMMMd)\r\n\t\t\tformatters[locale].dayMonthYear = new Intl.DateTimeFormat(locale, {\r\n\t\t\t\tyear: 'numeric',\r\n\t\t\t\tmonth: 'short',\r\n\t\t\t\tday: 'numeric'\r\n\t\t\t})\r\n\t\t}\r\n\t\t// Output day, month and year.\r\n\t\treturn formatters[locale].dayMonthYear.format(getDate(value))\r\n\t}\r\n}\r\n\r\n// If `Intl.DateTimeFormat` is supported,\r\n// then longer time intervals will be formatted as dates.\r\n/* istanbul ignore else */\r\nif (intlDateTimeFormatSupported()) {\r\n\tsteps.push(monthAndDay, yearMonthAndDay)\r\n}\r\n// Otherwise, if `Intl.DateTimeFormat` is not supported,\r\n// which could be the case when using Internet Explorer,\r\n// then simply mimick \"round\" steps.\r\nelse {\r\n\tsteps.push(\r\n\t\t{\r\n\t\t\tformatAs: 'day'\r\n\t\t},\r\n\t\t{\r\n\t\t\tformatAs: 'week'\r\n\t\t},\r\n\t\t{\r\n\t\t\tformatAs: 'month'\r\n\t\t},\r\n\t\t{\r\n\t\t\tformatAs: 'year'\r\n\t\t}\r\n\t)\r\n}\r\n\r\nexport default {\r\n\tsteps,\r\n\tlabels: [\r\n\t\t// \"mini\" labels are only defined for a few languages.\r\n\t\t'mini',\r\n\t\t// \"short-time\" labels are only defined for a few languages.\r\n\t\t'short-time',\r\n\t\t// \"narrow\" and \"short\" labels are defined for all languages.\r\n\t\t// \"narrow\" labels can sometimes be weird (like \"+5d.\"),\r\n\t\t// but \"short\" labels have the \" ago\" part, so \"narrow\" seem\r\n\t\t// more appropriate.\r\n\t\t// \"short\" labels would have been more appropriate if they\r\n\t\t// didn't have the \" ago\" part, hence the \"short-time\" above.\r\n\t\t'narrow',\r\n\t\t// Since \"narrow\" labels are always present, \"short\" element\r\n\t\t// of this array can be removed.\r\n\t\t'short'\r\n\t]\r\n}"], "mappings": "AAAA,SAASA,GAAT,EAAcC,OAAd,QAA6B,mBAA7B;AACA,SAASC,2BAAT,QAA4C,cAA5C,C,CAEA;;AACA,OAAOC,sBAAP,MAAmC,6BAAnC,C,CAEA;AACA;AACA;AACA;AACA;;AAEA,IAAMC,KAAK,GAAG,CACb;EACCC,QAAQ,EAAE;AADX,CADa,EAIb;EACCA,QAAQ,EAAE;AADX,CAJa,EAOb;EACCA,QAAQ,EAAE;AADX,CAPa,CAAd,C,CAYA;AACA;;AACA,IAAMC,UAAU,GAAG,EAAnB,C,CAEA;;AACA,IAAMC,WAAW,GAAG;EACnBC,OADmB,mBACXC,SADW,QAC+B;IAAA,IAA7BC,MAA6B,QAA7BA,MAA6B;IAAA,IAArBC,iBAAqB,QAArBA,iBAAqB;IACjD;IACA;IACA,OAAOA,iBAAiB,CAAC,KAAD,CAAxB;EACA,CALkB;EAMnBC,MANmB,kBAMZC,KANY,EAMLC,MANK,EAMG;IACrB;IACA,IAAI,CAACR,UAAU,CAACQ,MAAD,CAAf,EAAyB;MACxBR,UAAU,CAACQ,MAAD,CAAV,GAAqB,EAArB;IACA;IACD;;;IACA,IAAI,CAACR,UAAU,CAACQ,MAAD,CAAV,CAAmBC,QAAxB,EAAkC;MACjC;MACAT,UAAU,CAACQ,MAAD,CAAV,CAAmBC,QAAnB,GAA8B,IAAIC,IAAI,CAACC,cAAT,CAAwBH,MAAxB,EAAgC;QAC7DI,KAAK,EAAE,OADsD;QAE7DlB,GAAG,EAAE;MAFwD,CAAhC,CAA9B;IAIA,CAZoB,CAarB;;;IACA,OAAOM,UAAU,CAACQ,MAAD,CAAV,CAAmBC,QAAnB,CAA4BH,MAA5B,CAAmCX,OAAO,CAACY,KAAD,CAA1C,CAAP;EACA;AArBkB,CAApB,C,CAwBA;AACA;AACA;;AACA,IAAMM,eAAe,GAAG;EACvBX,OADuB,mBACfC,SADe,SACQ;IAAA,IAAVC,MAAU,SAAVA,MAAU;;IAC9B,IAAIA,MAAJ,EAAY;MACX;MACA;MACA;MACA;MACA;MACA;MACA;MACA,IAAMU,aAAa,GAAG,IAAIC,IAAJ,CAAS,IAAIA,IAAJ,CAASZ,SAAT,EAAoBa,WAApB,EAAT,EAA4C,CAA5C,EAA+CC,OAA/C,KAA2D,CAAjF,CARW,CASX;;MACA,OAAO,CAACd,SAAS,GAAGW,aAAb,IAA8B,IAArC;IACA,CAXD,MAWO;MACN;MACA;MACA;MACA;MACA;MACA,IAAMI,aAAa,GAAG,IAAIH,IAAJ,CAAS,IAAIA,IAAJ,CAASZ,SAAT,EAAoBa,WAApB,KAAoC,CAA7C,EAAgD,CAAhD,EAAmDC,OAAnD,EAAtB,CANM,CAON;;MACA,OAAO,CAACC,aAAa,GAAGf,SAAjB,IAA8B,IAArC;IACA;EACD,CAvBsB;EAwBvBG,MAxBuB,kBAwBhBC,KAxBgB,EAwBTC,MAxBS,EAwBD;IACrB;IACA,IAAI,CAACR,UAAU,CAACQ,MAAD,CAAf,EAAyB;MACxBR,UAAU,CAACQ,MAAD,CAAV,GAAqB,EAArB;IACA;IACD;;;IACA,IAAI,CAACR,UAAU,CAACQ,MAAD,CAAV,CAAmBW,YAAxB,EAAsC;MACrC;MACAnB,UAAU,CAACQ,MAAD,CAAV,CAAmBW,YAAnB,GAAkC,IAAIT,IAAI,CAACC,cAAT,CAAwBH,MAAxB,EAAgC;QACjEY,IAAI,EAAE,SAD2D;QAEjER,KAAK,EAAE,OAF0D;QAGjElB,GAAG,EAAE;MAH4D,CAAhC,CAAlC;IAKA,CAboB,CAcrB;;;IACA,OAAOM,UAAU,CAACQ,MAAD,CAAV,CAAmBW,YAAnB,CAAgCb,MAAhC,CAAuCX,OAAO,CAACY,KAAD,CAA9C,CAAP;EACA;AAxCsB,CAAxB,C,CA2CA;AACA;;AACA;;AACA,IAAIX,2BAA2B,EAA/B,EAAmC;EAClCE,KAAK,CAACuB,IAAN,CAAWpB,WAAX,EAAwBY,eAAxB;AACA,CAFD,CAGA;AACA;AACA;AALA,KAMK;EACJf,KAAK,CAACuB,IAAN,CACC;IACCtB,QAAQ,EAAE;EADX,CADD,EAIC;IACCA,QAAQ,EAAE;EADX,CAJD,EAOC;IACCA,QAAQ,EAAE;EADX,CAPD,EAUC;IACCA,QAAQ,EAAE;EADX,CAVD;AAcA;;AAED,eAAe;EACdD,KAAK,EAALA,KADc;EAEdwB,MAAM,EAAE,CACP;EACA,MAFO,EAGP;EACA,YAJO,EAKP;EACA;EACA;EACA;EACA;EACA;EACA,QAXO,EAYP;EACA;EACA,OAdO;AAFM,CAAf"}