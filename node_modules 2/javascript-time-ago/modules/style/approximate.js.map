{"version": 3, "file": "approximate.js", "names": ["approximate", "gradation", "flavour", "units"], "sources": ["../../source/style/approximate.js"], "sourcesContent": ["import approximate from '../steps/approximate.js'\r\n\r\n// \"gradation\" is a legacy name for \"steps\".\r\n// It's here just for legacy compatibility.\r\n// Use \"steps\" name instead.\r\n\r\n// \"flavour\" is a legacy name for \"labels\".\r\n// It's here just for legacy compatibility.\r\n// Use \"labels\" name instead.\r\n\r\n// \"units\" is a legacy property.\r\n// It's here just for legacy compatibility.\r\n// Developers shouldn't need to use it in their custom styles.\r\n\r\nexport default {\r\n\tgradation: approximate,\r\n\tflavour: 'long',\r\n\tunits: [\r\n\t\t'now',\r\n\t\t'minute',\r\n\t\t'hour',\r\n\t\t'day',\r\n\t\t'week',\r\n\t\t'month',\r\n\t\t'year'\r\n\t]\r\n}"], "mappings": "AAAA,OAAOA,WAAP,MAAwB,yBAAxB,C,CAEA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;;AAEA,eAAe;EACdC,SAAS,EAAED,WADG;EAEdE,OAAO,EAAE,MAFK;EAGdC,KAAK,EAAE,CACN,KADM,EAEN,QAFM,EAGN,MAHM,EAIN,KAJM,EAKN,MALM,EAMN,OANM,EAON,MAPM;AAHO,CAAf"}