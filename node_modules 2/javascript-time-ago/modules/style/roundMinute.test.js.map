{"version": 3, "file": "roundMinute.test.js", "names": ["roundMinute", "TimeAgo", "day", "month", "year", "describe", "it", "timeAgo", "now", "Date", "getTime", "formatInterval", "secondsPassed", "format", "round", "should", "equal"], "sources": ["../../source/style/roundMinute.test.js"], "sourcesContent": ["import roundMinute from './roundMinute.js'\r\nimport TimeAgo from '../TimeAgo.js'\r\nimport { day, month, year } from '../steps/index.js'\r\n\r\ndescribe('style/round-minute', () => {\r\n\tit('should format relative time (English) (round: \"floor\")', () => {\r\n\t\tconst timeAgo = new TimeAgo('en')\r\n\r\n\t\tconst now = new Date(2016, 3, 10, 22, 59).getTime()\r\n\t\tconst formatInterval = (secondsPassed) => timeAgo.format(now - secondsPassed * 1000, { now, ...roundMinute, round: 'floor' })\r\n\r\n\t\tformatInterval(0).should.equal('just now')\r\n\t\tformatInterval(0.9).should.equal('just now')\r\n\t\tformatInterval(1).should.equal('just now')\r\n\t\tformatInterval(59.9).should.equal('just now')\r\n\t\tformatInterval(60).should.equal('1 minute ago')\r\n\t\tformatInterval(1.9 * 60).should.equal('1 minute ago')\r\n\t\tformatInterval(2 * 60).should.equal('2 minutes ago')\r\n\t\tformatInterval(2.9 * 60).should.equal('2 minutes ago')\r\n\t\tformatInterval(3 * 60).should.equal('3 minutes ago')\r\n\t\t// …\r\n\t\tformatInterval(59.9 * 60).should.equal('59 minutes ago')\r\n\t\tformatInterval(60 * 60).should.equal('1 hour ago')\r\n\t\tformatInterval(1.9 * 60 * 60).should.equal('1 hour ago')\r\n\t\tformatInterval(2 * 60 * 60).should.equal('2 hours ago')\r\n\t\tformatInterval(2.9 * 60 * 60).should.equal('2 hours ago')\r\n\t\tformatInterval(3 * 60 * 60).should.equal('3 hours ago')\r\n\t\t// …\r\n\t\tformatInterval(23.9 * 60 * 60).should.equal('23 hours ago')\r\n\t\tformatInterval(24 * 60 * 60).should.equal('1 day ago')\r\n\t\tformatInterval(1.9 * day).should.equal('1 day ago')\r\n\t\tformatInterval(2 * day).should.equal('2 days ago')\r\n\t\tformatInterval(2.9 * day).should.equal('2 days ago')\r\n\t\tformatInterval(3 * day).should.equal('3 days ago')\r\n\t\t// …\r\n\t\tformatInterval(6.9 * day).should.equal('6 days ago')\r\n\t\tformatInterval(7 * day).should.equal('1 week ago')\r\n\t\t// …\r\n\t\tformatInterval(3.9 * 7 * day).should.equal('3 weeks ago')\r\n\t\tformatInterval(4 * 7 * day).should.equal('4 weeks ago')\r\n\t\tformatInterval(30.51 * day).should.equal('1 month ago')\r\n\t\tformatInterval(1.9 * month).should.equal('1 month ago')\r\n\t\tformatInterval(2 * month).should.equal('2 months ago')\r\n\t\tformatInterval(2.9 * month).should.equal('2 months ago')\r\n\t\tformatInterval(3 * month).should.equal('3 months ago')\r\n\t\t// …\r\n\t\tformatInterval(11.9 * month).should.equal('11 months ago')\r\n\t\tformatInterval(12 * month).should.equal('1 year ago')\r\n\t\tformatInterval(1.99 * year).should.equal('1 year ago')\r\n\t\tformatInterval(2 * year).should.equal('2 years ago')\r\n\t\t// …\r\n\r\n\t\t// Test future dates.\r\n\t\tformatInterval(-1 * 3 * 60).should.equal('in 3 minutes')\r\n\t\tformatInterval(-1 * month * 8).should.equal('in 8 months')\r\n\t})\r\n\r\n\tit('should format relative time (English)', () => {\r\n\t\tconst timeAgo = new TimeAgo('en')\r\n\r\n\t\tconst now = new Date(2016, 3, 10, 22, 59).getTime()\r\n\t\tconst formatInterval = (secondsPassed) => timeAgo.format(now - secondsPassed * 1000, { now, ...roundMinute })\r\n\r\n\t\tformatInterval(0).should.equal('just now')\r\n\t\tformatInterval(0.49).should.equal('just now')\r\n\t\tformatInterval(0.5).should.equal('just now')\r\n\t\tformatInterval(29.9).should.equal('just now')\r\n\t\tformatInterval(30).should.equal('1 minute ago')\r\n\t\tformatInterval(1.49 * 60).should.equal('1 minute ago')\r\n\t\tformatInterval(1.5 * 60).should.equal('2 minutes ago')\r\n\t\tformatInterval(2.49 * 60).should.equal('2 minutes ago')\r\n\t\tformatInterval(2.5 * 60).should.equal('3 minutes ago')\r\n\t})\r\n})"], "mappings": ";;;;;;AAAA,OAAOA,WAAP,MAAwB,kBAAxB;AACA,OAAOC,OAAP,MAAoB,eAApB;AACA,SAASC,GAAT,EAAcC,KAAd,EAAqBC,IAArB,QAAiC,mBAAjC;AAEAC,QAAQ,CAAC,oBAAD,EAAuB,YAAM;EACpCC,EAAE,CAAC,wDAAD,EAA2D,YAAM;IAClE,IAAMC,OAAO,GAAG,IAAIN,OAAJ,CAAY,IAAZ,CAAhB;IAEA,IAAMO,GAAG,GAAG,IAAIC,IAAJ,CAAS,IAAT,EAAe,CAAf,EAAkB,EAAlB,EAAsB,EAAtB,EAA0B,EAA1B,EAA8BC,OAA9B,EAAZ;;IACA,IAAMC,cAAc,GAAG,SAAjBA,cAAiB,CAACC,aAAD;MAAA,OAAmBL,OAAO,CAACM,MAAR,CAAeL,GAAG,GAAGI,aAAa,GAAG,IAArC;QAA6CJ,GAAG,EAAHA;MAA7C,GAAqDR,WAArD;QAAkEc,KAAK,EAAE;MAAzE,GAAnB;IAAA,CAAvB;;IAEAH,cAAc,CAAC,CAAD,CAAd,CAAkBI,MAAlB,CAAyBC,KAAzB,CAA+B,UAA/B;IACAL,cAAc,CAAC,GAAD,CAAd,CAAoBI,MAApB,CAA2BC,KAA3B,CAAiC,UAAjC;IACAL,cAAc,CAAC,CAAD,CAAd,CAAkBI,MAAlB,CAAyBC,KAAzB,CAA+B,UAA/B;IACAL,cAAc,CAAC,IAAD,CAAd,CAAqBI,MAArB,CAA4BC,KAA5B,CAAkC,UAAlC;IACAL,cAAc,CAAC,EAAD,CAAd,CAAmBI,MAAnB,CAA0BC,KAA1B,CAAgC,cAAhC;IACAL,cAAc,CAAC,MAAM,EAAP,CAAd,CAAyBI,MAAzB,CAAgCC,KAAhC,CAAsC,cAAtC;IACAL,cAAc,CAAC,IAAI,EAAL,CAAd,CAAuBI,MAAvB,CAA8BC,KAA9B,CAAoC,eAApC;IACAL,cAAc,CAAC,MAAM,EAAP,CAAd,CAAyBI,MAAzB,CAAgCC,KAAhC,CAAsC,eAAtC;IACAL,cAAc,CAAC,IAAI,EAAL,CAAd,CAAuBI,MAAvB,CAA8BC,KAA9B,CAAoC,eAApC,EAdkE,CAelE;;IACAL,cAAc,CAAC,OAAO,EAAR,CAAd,CAA0BI,MAA1B,CAAiCC,KAAjC,CAAuC,gBAAvC;IACAL,cAAc,CAAC,KAAK,EAAN,CAAd,CAAwBI,MAAxB,CAA+BC,KAA/B,CAAqC,YAArC;IACAL,cAAc,CAAC,MAAM,EAAN,GAAW,EAAZ,CAAd,CAA8BI,MAA9B,CAAqCC,KAArC,CAA2C,YAA3C;IACAL,cAAc,CAAC,IAAI,EAAJ,GAAS,EAAV,CAAd,CAA4BI,MAA5B,CAAmCC,KAAnC,CAAyC,aAAzC;IACAL,cAAc,CAAC,MAAM,EAAN,GAAW,EAAZ,CAAd,CAA8BI,MAA9B,CAAqCC,KAArC,CAA2C,aAA3C;IACAL,cAAc,CAAC,IAAI,EAAJ,GAAS,EAAV,CAAd,CAA4BI,MAA5B,CAAmCC,KAAnC,CAAyC,aAAzC,EArBkE,CAsBlE;;IACAL,cAAc,CAAC,OAAO,EAAP,GAAY,EAAb,CAAd,CAA+BI,MAA/B,CAAsCC,KAAtC,CAA4C,cAA5C;IACAL,cAAc,CAAC,KAAK,EAAL,GAAU,EAAX,CAAd,CAA6BI,MAA7B,CAAoCC,KAApC,CAA0C,WAA1C;IACAL,cAAc,CAAC,MAAMT,GAAP,CAAd,CAA0Ba,MAA1B,CAAiCC,KAAjC,CAAuC,WAAvC;IACAL,cAAc,CAAC,IAAIT,GAAL,CAAd,CAAwBa,MAAxB,CAA+BC,KAA/B,CAAqC,YAArC;IACAL,cAAc,CAAC,MAAMT,GAAP,CAAd,CAA0Ba,MAA1B,CAAiCC,KAAjC,CAAuC,YAAvC;IACAL,cAAc,CAAC,IAAIT,GAAL,CAAd,CAAwBa,MAAxB,CAA+BC,KAA/B,CAAqC,YAArC,EA5BkE,CA6BlE;;IACAL,cAAc,CAAC,MAAMT,GAAP,CAAd,CAA0Ba,MAA1B,CAAiCC,KAAjC,CAAuC,YAAvC;IACAL,cAAc,CAAC,IAAIT,GAAL,CAAd,CAAwBa,MAAxB,CAA+BC,KAA/B,CAAqC,YAArC,EA/BkE,CAgClE;;IACAL,cAAc,CAAC,MAAM,CAAN,GAAUT,GAAX,CAAd,CAA8Ba,MAA9B,CAAqCC,KAArC,CAA2C,aAA3C;IACAL,cAAc,CAAC,IAAI,CAAJ,GAAQT,GAAT,CAAd,CAA4Ba,MAA5B,CAAmCC,KAAnC,CAAyC,aAAzC;IACAL,cAAc,CAAC,QAAQT,GAAT,CAAd,CAA4Ba,MAA5B,CAAmCC,KAAnC,CAAyC,aAAzC;IACAL,cAAc,CAAC,MAAMR,KAAP,CAAd,CAA4BY,MAA5B,CAAmCC,KAAnC,CAAyC,aAAzC;IACAL,cAAc,CAAC,IAAIR,KAAL,CAAd,CAA0BY,MAA1B,CAAiCC,KAAjC,CAAuC,cAAvC;IACAL,cAAc,CAAC,MAAMR,KAAP,CAAd,CAA4BY,MAA5B,CAAmCC,KAAnC,CAAyC,cAAzC;IACAL,cAAc,CAAC,IAAIR,KAAL,CAAd,CAA0BY,MAA1B,CAAiCC,KAAjC,CAAuC,cAAvC,EAvCkE,CAwClE;;IACAL,cAAc,CAAC,OAAOR,KAAR,CAAd,CAA6BY,MAA7B,CAAoCC,KAApC,CAA0C,eAA1C;IACAL,cAAc,CAAC,KAAKR,KAAN,CAAd,CAA2BY,MAA3B,CAAkCC,KAAlC,CAAwC,YAAxC;IACAL,cAAc,CAAC,OAAOP,IAAR,CAAd,CAA4BW,MAA5B,CAAmCC,KAAnC,CAAyC,YAAzC;IACAL,cAAc,CAAC,IAAIP,IAAL,CAAd,CAAyBW,MAAzB,CAAgCC,KAAhC,CAAsC,aAAtC,EA5CkE,CA6ClE;IAEA;;IACAL,cAAc,CAAC,CAAC,CAAD,GAAK,CAAL,GAAS,EAAV,CAAd,CAA4BI,MAA5B,CAAmCC,KAAnC,CAAyC,cAAzC;IACAL,cAAc,CAAC,CAAC,CAAD,GAAKR,KAAL,GAAa,CAAd,CAAd,CAA+BY,MAA/B,CAAsCC,KAAtC,CAA4C,aAA5C;EACA,CAlDC,CAAF;EAoDAV,EAAE,CAAC,uCAAD,EAA0C,YAAM;IACjD,IAAMC,OAAO,GAAG,IAAIN,OAAJ,CAAY,IAAZ,CAAhB;IAEA,IAAMO,GAAG,GAAG,IAAIC,IAAJ,CAAS,IAAT,EAAe,CAAf,EAAkB,EAAlB,EAAsB,EAAtB,EAA0B,EAA1B,EAA8BC,OAA9B,EAAZ;;IACA,IAAMC,cAAc,GAAG,SAAjBA,cAAiB,CAACC,aAAD;MAAA,OAAmBL,OAAO,CAACM,MAAR,CAAeL,GAAG,GAAGI,aAAa,GAAG,IAArC;QAA6CJ,GAAG,EAAHA;MAA7C,GAAqDR,WAArD,EAAnB;IAAA,CAAvB;;IAEAW,cAAc,CAAC,CAAD,CAAd,CAAkBI,MAAlB,CAAyBC,KAAzB,CAA+B,UAA/B;IACAL,cAAc,CAAC,IAAD,CAAd,CAAqBI,MAArB,CAA4BC,KAA5B,CAAkC,UAAlC;IACAL,cAAc,CAAC,GAAD,CAAd,CAAoBI,MAApB,CAA2BC,KAA3B,CAAiC,UAAjC;IACAL,cAAc,CAAC,IAAD,CAAd,CAAqBI,MAArB,CAA4BC,KAA5B,CAAkC,UAAlC;IACAL,cAAc,CAAC,EAAD,CAAd,CAAmBI,MAAnB,CAA0BC,KAA1B,CAAgC,cAAhC;IACAL,cAAc,CAAC,OAAO,EAAR,CAAd,CAA0BI,MAA1B,CAAiCC,KAAjC,CAAuC,cAAvC;IACAL,cAAc,CAAC,MAAM,EAAP,CAAd,CAAyBI,MAAzB,CAAgCC,KAAhC,CAAsC,eAAtC;IACAL,cAAc,CAAC,OAAO,EAAR,CAAd,CAA0BI,MAA1B,CAAiCC,KAAjC,CAAuC,eAAvC;IACAL,cAAc,CAAC,MAAM,EAAP,CAAd,CAAyBI,MAAzB,CAAgCC,KAAhC,CAAsC,eAAtC;EACA,CAfC,CAAF;AAgBA,CArEO,CAAR"}