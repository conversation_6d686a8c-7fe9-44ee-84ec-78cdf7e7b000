{"version": 3, "file": "miniNow.js", "names": ["mini", "steps", "formatAs", "concat"], "sources": ["../../source/style/miniNow.js"], "sourcesContent": ["import mini from './mini.js'\r\n\r\nexport default {\r\n\t...mini,\r\n\t// Add \"now\".\r\n\tsteps: [{ formatAs: 'now' }].concat(mini.steps)\r\n}"], "mappings": ";;;;;;AAAA,OAAOA,IAAP,MAAiB,WAAjB;AAEA,+CACIA,IADJ;EAEC;EACAC,KAAK,EAAE,CAAC;IAAEC,QAAQ,EAAE;EAAZ,CAAD,EAAsBC,MAAtB,CAA6BH,IAAI,CAACC,KAAlC;AAHR"}