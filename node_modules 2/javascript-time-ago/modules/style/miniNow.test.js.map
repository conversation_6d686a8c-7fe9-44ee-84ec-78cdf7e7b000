{"version": 3, "file": "miniNow.test.js", "names": ["style", "TimeAgo", "hour", "minute", "day", "month", "year", "describe", "it", "timeAgo", "formatInterval", "secondsPassed", "format", "now", "round", "should", "equal"], "sources": ["../../source/style/miniNow.test.js"], "sourcesContent": ["import style from './miniNow.js'\r\nimport TimeAgo from '../TimeAgo.js'\r\nimport { hour, minute, day, month, year } from '../steps/index.js'\r\n\r\ndescribe('style/mini-now', () => {\r\n\tit('should format relative date/time (round: \"floor\")', () => {\r\n\t\tconst timeAgo = new TimeAgo('en')\r\n\t\tconst formatInterval = (secondsPassed) => timeAgo.format(-secondsPassed * 1000, { now: 0, ...style, round: 'floor' })\r\n\r\n\t\tformatInterval(0).should.equal('now')\r\n\t\tformatInterval(0.9).should.equal('now')\r\n\t\tformatInterval(1).should.equal('1s')\r\n\t\tformatInterval(59.9).should.equal('59s')\r\n\t\tformatInterval(60).should.equal('1m')\r\n\t\tformatInterval(1.9 * minute).should.equal('1m')\r\n\t\tformatInterval(2 * minute).should.equal('2m')\r\n\t\tformatInterval(2.9 * minute).should.equal('2m')\r\n\t\tformatInterval(3 * minute).should.equal('3m')\r\n\t\t// …\r\n\t\tformatInterval(59.9 * minute).should.equal('59m')\r\n\t\tformatInterval(60 * minute).should.equal('1h')\r\n\t\tformatInterval(1.9 * hour).should.equal('1h')\r\n\t\tformatInterval(2 * hour).should.equal('2h')\r\n\t\tformatInterval(2.9 * hour).should.equal('2h')\r\n\t\tformatInterval(3 * hour).should.equal('3h')\r\n\t\t// …\r\n\t\tformatInterval(23.9 * hour).should.equal('23h')\r\n\t\tformatInterval(24 * hour).should.equal('1d')\r\n\t\tformatInterval(2 * day).should.equal('2d')\r\n\t\tformatInterval(7 * day).should.equal('7d')\r\n\t\tformatInterval(30 * day).should.equal('30d')\r\n\t\tformatInterval(month).should.equal('1mo')\r\n\t\tformatInterval(360 * day).should.equal('11mo')\r\n\t\tformatInterval(366 * day).should.equal('1yr')\r\n\t})\r\n})"], "mappings": ";;;;;;AAAA,OAAOA,KAAP,MAAkB,cAAlB;AACA,OAAOC,OAAP,MAAoB,eAApB;AACA,SAASC,IAAT,EAAeC,MAAf,EAAuBC,GAAvB,EAA4BC,KAA5B,EAAmCC,IAAnC,QAA+C,mBAA/C;AAEAC,QAAQ,CAAC,gBAAD,EAAmB,YAAM;EAChCC,EAAE,CAAC,mDAAD,EAAsD,YAAM;IAC7D,IAAMC,OAAO,GAAG,IAAIR,OAAJ,CAAY,IAAZ,CAAhB;;IACA,IAAMS,cAAc,GAAG,SAAjBA,cAAiB,CAACC,aAAD;MAAA,OAAmBF,OAAO,CAACG,MAAR,CAAe,CAACD,aAAD,GAAiB,IAAhC;QAAwCE,GAAG,EAAE;MAA7C,GAAmDb,KAAnD;QAA0Dc,KAAK,EAAE;MAAjE,GAAnB;IAAA,CAAvB;;IAEAJ,cAAc,CAAC,CAAD,CAAd,CAAkBK,MAAlB,CAAyBC,KAAzB,CAA+B,KAA/B;IACAN,cAAc,CAAC,GAAD,CAAd,CAAoBK,MAApB,CAA2BC,KAA3B,CAAiC,KAAjC;IACAN,cAAc,CAAC,CAAD,CAAd,CAAkBK,MAAlB,CAAyBC,KAAzB,CAA+B,IAA/B;IACAN,cAAc,CAAC,IAAD,CAAd,CAAqBK,MAArB,CAA4BC,KAA5B,CAAkC,KAAlC;IACAN,cAAc,CAAC,EAAD,CAAd,CAAmBK,MAAnB,CAA0BC,KAA1B,CAAgC,IAAhC;IACAN,cAAc,CAAC,MAAMP,MAAP,CAAd,CAA6BY,MAA7B,CAAoCC,KAApC,CAA0C,IAA1C;IACAN,cAAc,CAAC,IAAIP,MAAL,CAAd,CAA2BY,MAA3B,CAAkCC,KAAlC,CAAwC,IAAxC;IACAN,cAAc,CAAC,MAAMP,MAAP,CAAd,CAA6BY,MAA7B,CAAoCC,KAApC,CAA0C,IAA1C;IACAN,cAAc,CAAC,IAAIP,MAAL,CAAd,CAA2BY,MAA3B,CAAkCC,KAAlC,CAAwC,IAAxC,EAZ6D,CAa7D;;IACAN,cAAc,CAAC,OAAOP,MAAR,CAAd,CAA8BY,MAA9B,CAAqCC,KAArC,CAA2C,KAA3C;IACAN,cAAc,CAAC,KAAKP,MAAN,CAAd,CAA4BY,MAA5B,CAAmCC,KAAnC,CAAyC,IAAzC;IACAN,cAAc,CAAC,MAAMR,IAAP,CAAd,CAA2Ba,MAA3B,CAAkCC,KAAlC,CAAwC,IAAxC;IACAN,cAAc,CAAC,IAAIR,IAAL,CAAd,CAAyBa,MAAzB,CAAgCC,KAAhC,CAAsC,IAAtC;IACAN,cAAc,CAAC,MAAMR,IAAP,CAAd,CAA2Ba,MAA3B,CAAkCC,KAAlC,CAAwC,IAAxC;IACAN,cAAc,CAAC,IAAIR,IAAL,CAAd,CAAyBa,MAAzB,CAAgCC,KAAhC,CAAsC,IAAtC,EAnB6D,CAoB7D;;IACAN,cAAc,CAAC,OAAOR,IAAR,CAAd,CAA4Ba,MAA5B,CAAmCC,KAAnC,CAAyC,KAAzC;IACAN,cAAc,CAAC,KAAKR,IAAN,CAAd,CAA0Ba,MAA1B,CAAiCC,KAAjC,CAAuC,IAAvC;IACAN,cAAc,CAAC,IAAIN,GAAL,CAAd,CAAwBW,MAAxB,CAA+BC,KAA/B,CAAqC,IAArC;IACAN,cAAc,CAAC,IAAIN,GAAL,CAAd,CAAwBW,MAAxB,CAA+BC,KAA/B,CAAqC,IAArC;IACAN,cAAc,CAAC,KAAKN,GAAN,CAAd,CAAyBW,MAAzB,CAAgCC,KAAhC,CAAsC,KAAtC;IACAN,cAAc,CAACL,KAAD,CAAd,CAAsBU,MAAtB,CAA6BC,KAA7B,CAAmC,KAAnC;IACAN,cAAc,CAAC,MAAMN,GAAP,CAAd,CAA0BW,MAA1B,CAAiCC,KAAjC,CAAuC,MAAvC;IACAN,cAAc,CAAC,MAAMN,GAAP,CAAd,CAA0BW,MAA1B,CAAiCC,KAAjC,CAAuC,KAAvC;EACA,CA7BC,CAAF;AA8BA,CA/BO,CAAR"}