{"version": 3, "file": "miniMinute.js", "names": ["mini", "steps", "filter", "step", "formatAs"], "sources": ["../../source/style/miniMinute.js"], "sourcesContent": ["import mini from './mini.js'\r\n\r\nexport default {\r\n\t...mini,\r\n\t// Skip \"seconds\".\r\n\tsteps: mini.steps.filter(step => step.formatAs !== 'second')\r\n}"], "mappings": ";;;;;;AAAA,OAAOA,IAAP,MAAiB,WAAjB;AAEA,+CACIA,IADJ;EAEC;EACAC,KAAK,EAAED,IAAI,CAACC,KAAL,CAAWC,MAAX,CAAkB,UAAAC,IAAI;IAAA,OAAIA,IAAI,CAACC,QAAL,KAAkB,QAAtB;EAAA,CAAtB;AAHR"}