{"version": 3, "file": "renameLegacyProperties.test.js", "names": ["renameLegacyProperties", "describe", "it", "steps", "unit", "minTime", "week", "labels", "should", "deep", "equal", "gradation", "threshold", "threshold_for_week", "flavour", "custom"], "sources": ["../../source/style/renameLegacyProperties.test.js"], "sourcesContent": ["import renameLegacyProperties from './renameLegacyProperties.js'\r\n\r\ndescribe('style/renameLegacyProperties', () => {\r\n\tit('should rename legacy properties', () => {\r\n\t\trenameLegacyProperties({\r\n\t\t\tsteps: [{\r\n\t\t\t\tunit: 'now',\r\n\t\t\t\tminTime: {\r\n\t\t\t\t\tweek: 2,\r\n\t\t\t\t\tdefault: 1\r\n\t\t\t\t}\r\n\t\t\t}],\r\n\t\t\tlabels: 'long'\r\n\t\t}).should.deep.equal({\r\n\t\t\tgradation: [{\r\n\t\t\t\tunit: 'now',\r\n\t\t\t\tthreshold: 1,\r\n\t\t\t\tthreshold_for_week: 2\r\n\t\t\t}],\r\n\t\t\tflavour: 'long'\r\n\t\t})\r\n\t})\r\n\r\n\tit('should cover edge cases', () => {\r\n\t\tconst custom = () => {}\r\n\t\trenameLegacyProperties({\r\n\t\t\tcustom\r\n\t\t}).should.deep.equal({\r\n\t\t\tcustom\r\n\t\t})\r\n\t})\r\n})"], "mappings": "AAAA,OAAOA,sBAAP,MAAmC,6BAAnC;AAEAC,QAAQ,CAAC,8BAAD,EAAiC,YAAM;EAC9CC,EAAE,CAAC,iCAAD,EAAoC,YAAM;IAC3CF,sBAAsB,CAAC;MACtBG,KAAK,EAAE,CAAC;QACPC,IAAI,EAAE,KADC;QAEPC,OAAO,EAAE;UACRC,IAAI,EAAE,CADE;UAER,WAAS;QAFD;MAFF,CAAD,CADe;MAQtBC,MAAM,EAAE;IARc,CAAD,CAAtB,CASGC,MATH,CASUC,IATV,CASeC,KATf,CASqB;MACpBC,SAAS,EAAE,CAAC;QACXP,IAAI,EAAE,KADK;QAEXQ,SAAS,EAAE,CAFA;QAGXC,kBAAkB,EAAE;MAHT,CAAD,CADS;MAMpBC,OAAO,EAAE;IANW,CATrB;EAiBA,CAlBC,CAAF;EAoBAZ,EAAE,CAAC,yBAAD,EAA4B,YAAM;IACnC,IAAMa,MAAM,GAAG,SAATA,MAAS,GAAM,CAAE,CAAvB;;IACAf,sBAAsB,CAAC;MACtBe,MAAM,EAANA;IADsB,CAAD,CAAtB,CAEGP,MAFH,CAEUC,IAFV,CAEeC,KAFf,CAEqB;MACpBK,MAAM,EAANA;IADoB,CAFrB;EAKA,CAPC,CAAF;AAQA,CA7BO,CAAR"}