"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("react"),t=require("three"),r=require("@react-three/fiber"),n=require("../../core/Line.cjs.js"),o=require("../Html.cjs.js"),a=require("./context.cjs.js");function i(e){if(e&&e.__esModule)return e;var t=Object.create(null);return e&&Object.keys(e).forEach((function(r){if("default"!==r){var n=Object.getOwnPropertyDescriptor(e,r);Object.defineProperty(t,r,n.get?n:{enumerable:!0,get:function(){return e[r]}})}})),t.default=e,Object.freeze(t)}require("@babel/runtime/helpers/extends"),require("three-stdlib"),require("react-dom/client");var s=i(e),c=i(t);const l=new c.Ray,u=new c.Vector3,p=new c.Matrix4;exports.PlaneSlider=({dir1:e,dir2:t,axis:i})=>{const{translation:d,translationLimits:m,annotations:x,annotationsClass:b,depthTest:g,scale:f,lineWidth:h,fixed:M,axisColors:y,hoveredColor:C,opacity:P,onDragStart:v,onDrag:w,onDragEnd:j,userData:O}=s.useContext(a.context),k=r.useThree((e=>e.controls)),F=s.useRef(null),z=s.useRef(null),E=s.useRef(null),V=s.useRef(0),q=s.useRef(0),[D,R]=s.useState(!1),T=s.useCallback((e=>{x&&(F.current.innerText=`${d.current[(i+1)%3].toFixed(2)}, ${d.current[(i+2)%3].toFixed(2)}`,F.current.style.display="block"),e.stopPropagation();const t=e.point.clone(),r=(new c.Vector3).setFromMatrixPosition(z.current.matrixWorld),n=(new c.Vector3).setFromMatrixColumn(z.current.matrixWorld,0).normalize(),o=(new c.Vector3).setFromMatrixColumn(z.current.matrixWorld,1).normalize(),a=(new c.Vector3).setFromMatrixColumn(z.current.matrixWorld,2).normalize(),s=(new c.Plane).setFromNormalAndCoplanarPoint(a,r);E.current={clickPoint:t,e1:n,e2:o,plane:s},V.current=d.current[(i+1)%3],q.current=d.current[(i+2)%3],v({component:"Slider",axis:i,origin:r,directions:[n,o,a]}),k&&(k.enabled=!1),e.target.setPointerCapture(e.pointerId)}),[x,k,v,i]),S=s.useCallback((e=>{if(e.stopPropagation(),D||R(!0),E.current){const{clickPoint:t,e1:r,e2:n,plane:o}=E.current,[a,s]=(null==m?void 0:m[(i+1)%3])||[void 0,void 0],[c,b]=(null==m?void 0:m[(i+2)%3])||[void 0,void 0];l.copy(e.ray),l.intersectPlane(o,u),l.direction.negate(),l.intersectPlane(o,u),u.sub(t);let[g,f]=((e,t,r)=>{const n=Math.abs(e.x)>=Math.abs(e.y)&&Math.abs(e.x)>=Math.abs(e.z)?0:Math.abs(e.y)>=Math.abs(e.x)&&Math.abs(e.y)>=Math.abs(e.z)?1:2,o=[0,1,2].sort(((e,r)=>Math.abs(t.getComponent(r))-Math.abs(t.getComponent(e)))),a=n===o[0]?o[1]:o[0],i=e.getComponent(n),s=e.getComponent(a),c=t.getComponent(n),l=t.getComponent(a),u=r.getComponent(n),p=(r.getComponent(a)-u*(s/i))/(l-c*(s/i));return[(u-p*c)/i,p]})(r,n,u);void 0!==a&&(g=Math.max(g,a-V.current)),void 0!==s&&(g=Math.min(g,s-V.current)),void 0!==c&&(f=Math.max(f,c-q.current)),void 0!==b&&(f=Math.min(f,b-q.current)),d.current[(i+1)%3]=V.current+g,d.current[(i+2)%3]=q.current+f,x&&(F.current.innerText=`${d.current[(i+1)%3].toFixed(2)}, ${d.current[(i+2)%3].toFixed(2)}`),p.makeTranslation(g*r.x+f*n.x,g*r.y+f*n.y,g*r.z+f*n.z),w(p)}}),[x,w,D,d,m,i]),W=s.useCallback((e=>{x&&(F.current.style.display="none"),e.stopPropagation(),E.current=null,j(),k&&(k.enabled=!0),e.target.releasePointerCapture(e.pointerId)}),[x,k,j]),$=s.useCallback((e=>{e.stopPropagation(),R(!1)}),[]),_=s.useMemo((()=>{const r=e.clone().normalize(),n=t.clone().normalize();return(new c.Matrix4).makeBasis(r,n,r.clone().cross(n))}),[e,t]),L=M?1/7:f/7,A=M?.225:.225*f,B=D?C:y[i],H=s.useMemo((()=>[new c.Vector3(0,0,0),new c.Vector3(0,A,0),new c.Vector3(A,A,0),new c.Vector3(A,0,0),new c.Vector3(0,0,0)]),[A]);return s.createElement("group",{ref:z,matrix:_,matrixAutoUpdate:!1},x&&s.createElement(o.Html,{position:[0,0,0]},s.createElement("div",{style:{display:"none",background:"#151520",color:"white",padding:"6px 8px",borderRadius:7,whiteSpace:"nowrap"},className:b,ref:F})),s.createElement("group",{position:[1.7*L,1.7*L,0]},s.createElement("mesh",{visible:!0,onPointerDown:T,onPointerMove:S,onPointerUp:W,onPointerOut:$,scale:A,userData:O},s.createElement("planeGeometry",null),s.createElement("meshBasicMaterial",{transparent:!0,depthTest:g,color:B,polygonOffset:!0,polygonOffsetFactor:-10,side:c.DoubleSide,fog:!1})),s.createElement(n.Line,{position:[-A/2,-A/2,0],transparent:!0,depthTest:g,points:H,lineWidth:h,color:B,opacity:P,polygonOffset:!0,polygonOffsetFactor:-10,userData:O,fog:!1})))};
