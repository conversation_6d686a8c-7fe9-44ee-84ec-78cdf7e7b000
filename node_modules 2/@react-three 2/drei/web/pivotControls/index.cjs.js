"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("@babel/runtime/helpers/extends"),r=require("@react-three/fiber"),t=require("react"),i=require("three"),a=require("./AxisArrow.cjs.js"),n=require("./AxisRotator.cjs.js"),o=require("./PlaneSlider.cjs.js"),c=require("./ScalingSphere.cjs.js"),l=require("./context.cjs.js"),s=require("../../core/calculateScaleFactor.cjs.js");function u(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}function d(e){if(e&&e.__esModule)return e;var r=Object.create(null);return e&&Object.keys(e).forEach((function(t){if("default"!==t){var i=Object.getOwnPropertyDescriptor(e,t);Object.defineProperty(r,t,i.get?i:{enumerable:!0,get:function(){return e[t]}})}})),r.default=e,Object.freeze(r)}require("../../core/Line.cjs.js"),require("three-stdlib"),require("../Html.cjs.js"),require("react-dom/client");var m=u(e),x=d(t),p=d(i);const f=new p.Matrix4,y=new p.Matrix4,w=new p.Matrix4,g=new p.Matrix4,b=new p.Matrix4,E=new p.Matrix4,j=new p.Matrix4,S=new p.Matrix4,h=new p.Matrix4,v=new p.Box3,M=new p.Box3,A=new p.Vector3,q=new p.Vector3,R=new p.Vector3,V=new p.Vector3,P=new p.Vector3,W=new p.Vector3(1,0,0),D=new p.Vector3(0,1,0),C=new p.Vector3(0,0,1),L=x.forwardRef((({enabled:e=!0,matrix:t,onDragStart:i,onDrag:u,onDragEnd:d,autoTransform:L=!0,anchor:O,disableAxes:B=!1,disableSliders:F=!1,disableRotations:z=!1,disableScaling:T=!1,activeAxes:_=[!0,!0,!0],offset:k=[0,0,0],rotation:H=[0,0,0],scale:I=1,lineWidth:U=4,fixed:G=!1,translationLimits:J,rotationLimits:K,scaleLimits:N,depthTest:Q=!0,axisColors:X=["#ff2060","#20df80","#2080ff"],hoveredColor:Y="#ffff40",annotations:Z=!1,annotationsClass:$,opacity:ee=1,visible:re=!0,userData:te,children:ie,...ae},ne)=>{const oe=r.useThree((e=>e.invalidate)),ce=x.useRef(null),le=x.useRef(null),se=x.useRef(null),ue=x.useRef(null),de=x.useRef([0,0,0]),me=x.useRef(new p.Vector3(1,1,1)),xe=x.useRef(new p.Vector3(1,1,1));x.useLayoutEffect((()=>{O&&(ue.current.updateWorldMatrix(!0,!0),g.copy(ue.current.matrixWorld).invert(),v.makeEmpty(),ue.current.traverse((e=>{e.geometry&&(e.geometry.boundingBox||e.geometry.computeBoundingBox(),E.copy(e.matrixWorld).premultiply(g),M.copy(e.geometry.boundingBox),M.applyMatrix4(E),v.union(M))})),A.copy(v.max).add(v.min).multiplyScalar(.5),q.copy(v.max).sub(v.min).multiplyScalar(.5),R.copy(q).multiply(new p.Vector3(...O)).add(A),V.set(...k).add(R),se.current.position.copy(V),oe())}));const pe=x.useMemo((()=>({onDragStart:e=>{f.copy(le.current.matrix),y.copy(le.current.matrixWorld),i&&i(e),oe()},onDrag:e=>{w.copy(ce.current.matrixWorld),g.copy(w).invert(),b.copy(y).premultiply(e),E.copy(b).premultiply(g),j.copy(f).invert(),S.copy(E).multiply(j),L&&le.current.matrix.copy(E),u&&u(E,S,b,e),oe()},onDragEnd:()=>{d&&d(),oe()},translation:de,translationLimits:J,rotationLimits:K,axisColors:X,hoveredColor:Y,opacity:ee,scale:I,lineWidth:U,fixed:G,depthTest:Q,userData:te,annotations:Z,annotationsClass:$})),[i,u,d,de,J,K,N,Q,I,U,G,...X,Y,ee,te,L,Z,$]),fe=new p.Vector3;return r.useFrame((e=>{if(G){const r=s.calculateScaleFactor(se.current.getWorldPosition(fe),I,e.camera,e.size);me.current.setScalar(r)}t&&t instanceof p.Matrix4&&(le.current.matrix=t),le.current.updateWorldMatrix(!0,!0),h.makeRotationFromEuler(se.current.rotation).setPosition(se.current.position).premultiply(le.current.matrixWorld),xe.current.setFromMatrixScale(h),P.copy(me.current).divide(xe.current),(Math.abs(se.current.scale.x-P.x)>1e-4||Math.abs(se.current.scale.y-P.y)>1e-4||Math.abs(se.current.scale.z-P.z)>1e-4)&&(se.current.scale.copy(P),e.invalidate())})),x.useImperativeHandle(ne,(()=>le.current),[]),x.createElement(l.context.Provider,{value:pe},x.createElement("group",{ref:ce},x.createElement("group",m.default({ref:le,matrix:t,matrixAutoUpdate:!1},ae),x.createElement("group",{visible:re,ref:se,position:k,rotation:H},e&&x.createElement(x.Fragment,null,!B&&_[0]&&x.createElement(a.AxisArrow,{axis:0,direction:W}),!B&&_[1]&&x.createElement(a.AxisArrow,{axis:1,direction:D}),!B&&_[2]&&x.createElement(a.AxisArrow,{axis:2,direction:C}),!F&&_[0]&&_[1]&&x.createElement(o.PlaneSlider,{axis:2,dir1:W,dir2:D}),!F&&_[0]&&_[2]&&x.createElement(o.PlaneSlider,{axis:1,dir1:C,dir2:W}),!F&&_[2]&&_[1]&&x.createElement(o.PlaneSlider,{axis:0,dir1:D,dir2:C}),!z&&_[0]&&_[1]&&x.createElement(n.AxisRotator,{axis:2,dir1:W,dir2:D}),!z&&_[0]&&_[2]&&x.createElement(n.AxisRotator,{axis:1,dir1:C,dir2:W}),!z&&_[2]&&_[1]&&x.createElement(n.AxisRotator,{axis:0,dir1:D,dir2:C}),!T&&_[0]&&x.createElement(c.ScalingSphere,{axis:0,direction:W}),!T&&_[1]&&x.createElement(c.ScalingSphere,{axis:1,direction:D}),!T&&_[2]&&x.createElement(c.ScalingSphere,{axis:2,direction:C}))),x.createElement("group",{ref:ue},ie))))}));exports.PivotControls=L;
