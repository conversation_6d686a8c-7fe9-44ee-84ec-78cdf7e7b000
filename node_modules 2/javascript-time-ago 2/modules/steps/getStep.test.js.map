{"version": 3, "file": "getStep.test.js", "names": ["getStep", "round", "describe", "it", "expect", "units", "to", "be", "undefined", "steps", "slice", "formatAs", "should", "equal", "granularity", "now", "firstStep", "splice", "unshift", "unit", "minTime", "id", "seconds", "threshold"], "sources": ["../../source/steps/getStep.test.js"], "sourcesContent": ["import getStep from './getStep.js'\r\nimport round from './round.js'\r\n\r\ndescribe('getStep', () => {\r\n\tit('should return nothing if no time units are supported', () => {\r\n\t\texpect(getStep(round, 0, { units: ['femtosecond'] })).to.be.undefined\r\n\t})\r\n\r\n\t// it('should throw if a non-first step does not have a `minTime` or `test()`', () => {\r\n\t// \texpect(getStep([{ unit: 'second' }], 2, { units: ['second'] })).to.deep.equal({ unit: 'second' })\r\n\t//\r\n\t// \texpect(() => {\r\n\t// \t\tgetStep([{ unit: 'second' }, { unit: 'minute' }], 2, { units: ['second', 'minute'] })\r\n\t// \t}).to.throw(\r\n\t// \t\t'Each step must define either `minTime` or `test()`, except for the first one. Got \"undefined\", undefined. Step: {\"unit\":\"minute\"}'\r\n\t// \t)\r\n\t// })\r\n\r\n\tit('should fall back to previous step if granularity is too high for the next step', () => {\r\n\t\tconst steps = round.slice()\r\n\r\n\t\tsteps[1].formatAs.should.equal('second')\r\n\t\tsteps[1].granularity = 3\r\n\r\n\t\tgetStep(steps, 1.49, { now: 0, units: ['now', 'second'] }).formatAs.should.equal('now')\r\n\r\n\t\t// And if there's no previous step, then use the current one.\r\n\r\n\t\tconst firstStep = steps[0]\r\n\t\tsteps.splice(0, 1)\r\n\r\n\t\tgetStep(steps, 1.49, { now: 0, units: ['now', 'second'] }).formatAs.should.equal('second')\r\n\r\n\t\tsteps.unshift(firstStep)\r\n\r\n\t\tdelete steps[1].granularity\r\n\t})\r\n\r\n\tit('should support `minTime` object', () => {\r\n\t\texpect(getStep(\r\n\t\t\t[\r\n\t\t\t\t{ unit: 'second' },\r\n\t\t\t\t{\r\n\t\t\t\t\tminTime: { default: 10 },\r\n\t\t\t\t\tunit: 'minute'\r\n\t\t\t\t}\r\n\t\t\t],\r\n\t\t\t5,\r\n\t\t\t{ now: 0, units: ['second', 'minute'] }\r\n\t\t).unit).to.equal('second')\r\n\r\n\t\texpect(getStep(\r\n\t\t\t[\r\n\t\t\t\t{ unit: 'second' },\r\n\t\t\t\t{\r\n\t\t\t\t\tminTime: { default: 10 },\r\n\t\t\t\t\tunit: 'minute'\r\n\t\t\t\t}\r\n\t\t\t],\r\n\t\t\t10,\r\n\t\t\t{ now: 0, units: ['second', 'minute'] }\r\n\t\t).unit).to.equal('minute')\r\n\r\n\t\texpect(getStep(\r\n\t\t\t[\r\n\t\t\t\t{\r\n\t\t\t\t\tid: 'seconds',\r\n\t\t\t\t\tunit: 'second'\r\n\t\t\t\t},\r\n\t\t\t\t{\r\n\t\t\t\t\tminTime: {\r\n\t\t\t\t\t\tseconds: 20,\r\n\t\t\t\t\t\tdefault: 10\r\n\t\t\t\t\t},\r\n\t\t\t\t\tunit: 'minute'\r\n\t\t\t\t}\r\n\t\t\t],\r\n\t\t\t10,\r\n\t\t\t{ now: 0, units: ['second', 'minute'] }\r\n\t\t).unit).to.equal('second')\r\n\t})\r\n\r\n\tit('should support legacy `threshold()` function', () => {\r\n\t\texpect(getStep(\r\n\t\t\t[\r\n\t\t\t\t{ unit: 'second' },\r\n\t\t\t\t{\r\n\t\t\t\t\tthreshold: () => 10,\r\n\t\t\t\t\tunit: 'minute'\r\n\t\t\t\t}\r\n\t\t\t],\r\n\t\t\t5,\r\n\t\t\t{ now: 0, units: ['second', 'minute'] }\r\n\t\t).unit).to.equal('second')\r\n\r\n\t\texpect(getStep(\r\n\t\t\t[\r\n\t\t\t\t{ unit: 'second' },\r\n\t\t\t\t{\r\n\t\t\t\t\tthreshold: () => 10,\r\n\t\t\t\t\tunit: 'minute'\r\n\t\t\t\t}\r\n\t\t\t],\r\n\t\t\t10,\r\n\t\t\t{ now: 0, units: ['second', 'minute'] }\r\n\t\t).unit).to.equal('minute')\r\n\t})\r\n\r\n\tit('should stop when reaching a step that has no \"minTime\" and for which \"minTime\" could not be deduced', () => {\r\n\t\texpect(getStep(\r\n\t\t\t[\r\n\t\t\t\t{ formatAs: 'second' },\r\n\t\t\t\t{ formatAs: 'unsupported-time-unit' }\r\n\t\t\t],\r\n\t\t\t10,\r\n\t\t\t{ now: 0, units: ['second', 'unsupported-time-unit'] }\r\n\t\t).formatAs).to.equal('second')\r\n\t})\r\n})"], "mappings": "AAAA,OAAOA,OAAP,MAAoB,cAApB;AACA,OAAOC,KAAP,MAAkB,YAAlB;AAEAC,QAAQ,CAAC,SAAD,EAAY,YAAM;EACzBC,EAAE,CAAC,sDAAD,EAAyD,YAAM;IAChEC,MAAM,CAACJ,OAAO,CAACC,KAAD,EAAQ,CAAR,EAAW;MAAEI,KAAK,EAAE,CAAC,aAAD;IAAT,CAAX,CAAR,CAAN,CAAsDC,EAAtD,CAAyDC,EAAzD,CAA4DC,SAA5D;EACA,CAFC,CAAF,CADyB,CAKzB;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EAEAL,EAAE,CAAC,gFAAD,EAAmF,YAAM;IAC1F,IAAMM,KAAK,GAAGR,KAAK,CAACS,KAAN,EAAd;IAEAD,KAAK,CAAC,CAAD,CAAL,CAASE,QAAT,CAAkBC,MAAlB,CAAyBC,KAAzB,CAA+B,QAA/B;IACAJ,KAAK,CAAC,CAAD,CAAL,CAASK,WAAT,GAAuB,CAAvB;IAEAd,OAAO,CAACS,KAAD,EAAQ,IAAR,EAAc;MAAEM,GAAG,EAAE,CAAP;MAAUV,KAAK,EAAE,CAAC,KAAD,EAAQ,QAAR;IAAjB,CAAd,CAAP,CAA2DM,QAA3D,CAAoEC,MAApE,CAA2EC,KAA3E,CAAiF,KAAjF,EAN0F,CAQ1F;;IAEA,IAAMG,SAAS,GAAGP,KAAK,CAAC,CAAD,CAAvB;IACAA,KAAK,CAACQ,MAAN,CAAa,CAAb,EAAgB,CAAhB;IAEAjB,OAAO,CAACS,KAAD,EAAQ,IAAR,EAAc;MAAEM,GAAG,EAAE,CAAP;MAAUV,KAAK,EAAE,CAAC,KAAD,EAAQ,QAAR;IAAjB,CAAd,CAAP,CAA2DM,QAA3D,CAAoEC,MAApE,CAA2EC,KAA3E,CAAiF,QAAjF;IAEAJ,KAAK,CAACS,OAAN,CAAcF,SAAd;IAEA,OAAOP,KAAK,CAAC,CAAD,CAAL,CAASK,WAAhB;EACA,CAlBC,CAAF;EAoBAX,EAAE,CAAC,iCAAD,EAAoC,YAAM;IAC3CC,MAAM,CAACJ,OAAO,CACb,CACC;MAAEmB,IAAI,EAAE;IAAR,CADD,EAEC;MACCC,OAAO,EAAE;QAAE,WAAS;MAAX,CADV;MAECD,IAAI,EAAE;IAFP,CAFD,CADa,EAQb,CARa,EASb;MAAEJ,GAAG,EAAE,CAAP;MAAUV,KAAK,EAAE,CAAC,QAAD,EAAW,QAAX;IAAjB,CATa,CAAP,CAULc,IAVI,CAAN,CAUQb,EAVR,CAUWO,KAVX,CAUiB,QAVjB;IAYAT,MAAM,CAACJ,OAAO,CACb,CACC;MAAEmB,IAAI,EAAE;IAAR,CADD,EAEC;MACCC,OAAO,EAAE;QAAE,WAAS;MAAX,CADV;MAECD,IAAI,EAAE;IAFP,CAFD,CADa,EAQb,EARa,EASb;MAAEJ,GAAG,EAAE,CAAP;MAAUV,KAAK,EAAE,CAAC,QAAD,EAAW,QAAX;IAAjB,CATa,CAAP,CAULc,IAVI,CAAN,CAUQb,EAVR,CAUWO,KAVX,CAUiB,QAVjB;IAYAT,MAAM,CAACJ,OAAO,CACb,CACC;MACCqB,EAAE,EAAE,SADL;MAECF,IAAI,EAAE;IAFP,CADD,EAKC;MACCC,OAAO,EAAE;QACRE,OAAO,EAAE,EADD;QAER,WAAS;MAFD,CADV;MAKCH,IAAI,EAAE;IALP,CALD,CADa,EAcb,EAda,EAeb;MAAEJ,GAAG,EAAE,CAAP;MAAUV,KAAK,EAAE,CAAC,QAAD,EAAW,QAAX;IAAjB,CAfa,CAAP,CAgBLc,IAhBI,CAAN,CAgBQb,EAhBR,CAgBWO,KAhBX,CAgBiB,QAhBjB;EAiBA,CA1CC,CAAF;EA4CAV,EAAE,CAAC,8CAAD,EAAiD,YAAM;IACxDC,MAAM,CAACJ,OAAO,CACb,CACC;MAAEmB,IAAI,EAAE;IAAR,CADD,EAEC;MACCI,SAAS,EAAE;QAAA,OAAM,EAAN;MAAA,CADZ;MAECJ,IAAI,EAAE;IAFP,CAFD,CADa,EAQb,CARa,EASb;MAAEJ,GAAG,EAAE,CAAP;MAAUV,KAAK,EAAE,CAAC,QAAD,EAAW,QAAX;IAAjB,CATa,CAAP,CAULc,IAVI,CAAN,CAUQb,EAVR,CAUWO,KAVX,CAUiB,QAVjB;IAYAT,MAAM,CAACJ,OAAO,CACb,CACC;MAAEmB,IAAI,EAAE;IAAR,CADD,EAEC;MACCI,SAAS,EAAE;QAAA,OAAM,EAAN;MAAA,CADZ;MAECJ,IAAI,EAAE;IAFP,CAFD,CADa,EAQb,EARa,EASb;MAAEJ,GAAG,EAAE,CAAP;MAAUV,KAAK,EAAE,CAAC,QAAD,EAAW,QAAX;IAAjB,CATa,CAAP,CAULc,IAVI,CAAN,CAUQb,EAVR,CAUWO,KAVX,CAUiB,QAVjB;EAWA,CAxBC,CAAF;EA0BAV,EAAE,CAAC,qGAAD,EAAwG,YAAM;IAC/GC,MAAM,CAACJ,OAAO,CACb,CACC;MAAEW,QAAQ,EAAE;IAAZ,CADD,EAEC;MAAEA,QAAQ,EAAE;IAAZ,CAFD,CADa,EAKb,EALa,EAMb;MAAEI,GAAG,EAAE,CAAP;MAAUV,KAAK,EAAE,CAAC,QAAD,EAAW,uBAAX;IAAjB,CANa,CAAP,CAOLM,QAPI,CAAN,CAOYL,EAPZ,CAOeO,KAPf,CAOqB,QAPrB;EAQA,CATC,CAAF;AAUA,CAnHO,CAAR"}