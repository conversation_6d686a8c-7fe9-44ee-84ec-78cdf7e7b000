{"version": 3, "file": "getStepMinTime.js", "names": ["getSecondsInUnit", "getDiffRatioToNextRoundedNumber", "getStepMinTime", "step", "prevStep", "timestamp", "now", "future", "round", "minTime", "id", "unit", "undefined", "threshold", "getMinTimeForUnit", "toUnit", "fromUnit", "formatAs", "test", "console", "warn", "JSON", "stringify", "toUnitAmount", "fromUnitAmount"], "sources": ["../../source/steps/getStepMinTime.js"], "sourcesContent": ["import { getSecondsInUnit } from './units.js'\r\nimport { getDiffRatioToNextRoundedNumber } from '../round.js'\r\n\r\nexport default function getStepMinTime(step, {\r\n\tprevStep,\r\n\ttimestamp,\r\n\t// `now` argument is used in a deprecated `step.test()` function.\r\n\tnow,\r\n\tfuture,\r\n\tround\r\n}) {\r\n\tlet minTime\r\n\t// \"threshold_for_xxx\" is a legacy property.\r\n\tif (prevStep) {\r\n\t\tif (prevStep.id || prevStep.unit) {\r\n\t\t\tminTime = step[`threshold_for_${prevStep.id || prevStep.unit}`]\r\n\t\t}\r\n\t}\r\n\tif (minTime === undefined) {\r\n\t\t// \"threshold\" is a legacy property.\r\n\t\tif (step.threshold !== undefined) {\r\n\t\t\t// \"threshold\" is a legacy name for \"minTime\".\r\n\t\t\tminTime = step.threshold\r\n\t\t\t// \"threshold\" function is deprecated.\r\n\t\t\tif (typeof minTime === 'function') {\r\n\t\t\t\tminTime = minTime(now, future)\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\tif (minTime === undefined) {\r\n\t\tminTime = step.minTime\r\n\t}\r\n\t// A deprecated way of specifying a different threshold\r\n\t// depending on the previous step's unit.\r\n\tif (typeof minTime === 'object') {\r\n\t\tif (prevStep && prevStep.id && minTime[prevStep.id] !== undefined) {\r\n\t\t\tminTime = minTime[prevStep.id]\r\n\t\t} else {\r\n\t\t\tminTime = minTime.default\r\n\t\t}\r\n\t}\r\n\tif (typeof minTime === 'function') {\r\n\t\tminTime = minTime(timestamp, {\r\n\t\t\tfuture,\r\n\t\t\tgetMinTimeForUnit(toUnit, fromUnit) {\r\n\t\t\t\treturn getMinTimeForUnit(\r\n\t\t\t\t\ttoUnit,\r\n\t\t\t\t\tfromUnit || prevStep && prevStep.formatAs,\r\n\t\t\t\t\t{ round }\r\n\t\t\t\t)\r\n\t\t\t}\r\n\t\t})\r\n\t}\r\n\t// Evaluate the `test()` function.\r\n\t// `test()` function is deprecated.\r\n\tif (minTime === undefined) {\r\n\t\tif (step.test) {\r\n\t\t\tif (step.test(timestamp, {\r\n\t\t\t\tnow,\r\n\t\t\t\tfuture\r\n\t\t\t})) {\r\n\t\t\t\t// `0` threshold always passes.\r\n\t\t\t\tminTime = 0\r\n\t\t\t} else {\r\n\t\t\t\t// `MAX_SAFE_INTEGER` threshold won't ever pass in real life.\r\n\t\t\t\tminTime = 9007199254740991 // Number.MAX_SAFE_INTEGER\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\tif (minTime === undefined) {\r\n\t\tif (prevStep) {\r\n\t\t\tif (step.formatAs && prevStep.formatAs) {\r\n\t\t\t\tminTime = getMinTimeForUnit(step.formatAs, prevStep.formatAs, { round })\r\n\t\t\t}\r\n\t\t} else {\r\n\t\t\t// The first step's `minTime` is `0` by default.\r\n\t\t\tminTime = 0\r\n\t\t}\r\n\t}\r\n\t// Warn if no `minTime` was defined or could be deduced.\r\n\tif (minTime === undefined) {\r\n\t\tconsole.warn('[javascript-time-ago] A step should specify `minTime`:\\n' + JSON.stringify(step, null, 2))\r\n\t}\r\n\treturn minTime\r\n}\r\n\r\nfunction getMinTimeForUnit(toUnit, fromUnit, { round }) {\r\n\tconst toUnitAmount = getSecondsInUnit(toUnit)\r\n\t// if (!fromUnit) {\r\n\t// \treturn toUnitAmount;\r\n\t// }\r\n\t// if (!fromUnit) {\r\n\t// \tfromUnit = getPreviousUnitFor(toUnit)\r\n\t// }\r\n\tlet fromUnitAmount\r\n\tif (fromUnit === 'now') {\r\n\t\tfromUnitAmount = getSecondsInUnit(toUnit)\r\n\t} else {\r\n\t\tfromUnitAmount = getSecondsInUnit(fromUnit)\r\n\t}\r\n\tif (toUnitAmount !== undefined && fromUnitAmount !== undefined) {\r\n\t\treturn toUnitAmount - fromUnitAmount * (1 - getDiffRatioToNextRoundedNumber(round))\r\n\t}\r\n}"], "mappings": ";;AAAA,SAASA,gBAAT,QAAiC,YAAjC;AACA,SAASC,+BAAT,QAAgD,aAAhD;AAEA,eAAe,SAASC,cAAT,CAAwBC,IAAxB,QAOZ;EAAA,IANFC,QAME,QANFA,QAME;EAAA,IALFC,SAKE,QALFA,SAKE;EAAA,IAHFC,GAGE,QAHFA,GAGE;EAAA,IAFFC,MAEE,QAFFA,MAEE;EAAA,IADFC,KACE,QADFA,KACE;EACF,IAAIC,OAAJ,CADE,CAEF;;EACA,IAAIL,QAAJ,EAAc;IACb,IAAIA,QAAQ,CAACM,EAAT,IAAeN,QAAQ,CAACO,IAA5B,EAAkC;MACjCF,OAAO,GAAGN,IAAI,yBAAkBC,QAAQ,CAACM,EAAT,IAAeN,QAAQ,CAACO,IAA1C,EAAd;IACA;EACD;;EACD,IAAIF,OAAO,KAAKG,SAAhB,EAA2B;IAC1B;IACA,IAAIT,IAAI,CAACU,SAAL,KAAmBD,SAAvB,EAAkC;MACjC;MACAH,OAAO,GAAGN,IAAI,CAACU,SAAf,CAFiC,CAGjC;;MACA,IAAI,OAAOJ,OAAP,KAAmB,UAAvB,EAAmC;QAClCA,OAAO,GAAGA,OAAO,CAACH,GAAD,EAAMC,MAAN,CAAjB;MACA;IACD;EACD;;EACD,IAAIE,OAAO,KAAKG,SAAhB,EAA2B;IAC1BH,OAAO,GAAGN,IAAI,CAACM,OAAf;EACA,CArBC,CAsBF;EACA;;;EACA,IAAI,QAAOA,OAAP,MAAmB,QAAvB,EAAiC;IAChC,IAAIL,QAAQ,IAAIA,QAAQ,CAACM,EAArB,IAA2BD,OAAO,CAACL,QAAQ,CAACM,EAAV,CAAP,KAAyBE,SAAxD,EAAmE;MAClEH,OAAO,GAAGA,OAAO,CAACL,QAAQ,CAACM,EAAV,CAAjB;IACA,CAFD,MAEO;MACND,OAAO,GAAGA,OAAO,WAAjB;IACA;EACD;;EACD,IAAI,OAAOA,OAAP,KAAmB,UAAvB,EAAmC;IAClCA,OAAO,GAAGA,OAAO,CAACJ,SAAD,EAAY;MAC5BE,MAAM,EAANA,MAD4B;MAE5BO,iBAF4B,6BAEVC,MAFU,EAEFC,QAFE,EAEQ;QACnC,OAAOF,kBAAiB,CACvBC,MADuB,EAEvBC,QAAQ,IAAIZ,QAAQ,IAAIA,QAAQ,CAACa,QAFV,EAGvB;UAAET,KAAK,EAALA;QAAF,CAHuB,CAAxB;MAKA;IAR2B,CAAZ,CAAjB;EAUA,CA1CC,CA2CF;EACA;;;EACA,IAAIC,OAAO,KAAKG,SAAhB,EAA2B;IAC1B,IAAIT,IAAI,CAACe,IAAT,EAAe;MACd,IAAIf,IAAI,CAACe,IAAL,CAAUb,SAAV,EAAqB;QACxBC,GAAG,EAAHA,GADwB;QAExBC,MAAM,EAANA;MAFwB,CAArB,CAAJ,EAGI;QACH;QACAE,OAAO,GAAG,CAAV;MACA,CAND,MAMO;QACN;QACAA,OAAO,GAAG,gBAAV,CAFM,CAEqB;MAC3B;IACD;EACD;;EACD,IAAIA,OAAO,KAAKG,SAAhB,EAA2B;IAC1B,IAAIR,QAAJ,EAAc;MACb,IAAID,IAAI,CAACc,QAAL,IAAiBb,QAAQ,CAACa,QAA9B,EAAwC;QACvCR,OAAO,GAAGK,kBAAiB,CAACX,IAAI,CAACc,QAAN,EAAgBb,QAAQ,CAACa,QAAzB,EAAmC;UAAET,KAAK,EAALA;QAAF,CAAnC,CAA3B;MACA;IACD,CAJD,MAIO;MACN;MACAC,OAAO,GAAG,CAAV;IACA;EACD,CApEC,CAqEF;;;EACA,IAAIA,OAAO,KAAKG,SAAhB,EAA2B;IAC1BO,OAAO,CAACC,IAAR,CAAa,6DAA6DC,IAAI,CAACC,SAAL,CAAenB,IAAf,EAAqB,IAArB,EAA2B,CAA3B,CAA1E;EACA;;EACD,OAAOM,OAAP;AACA;;AAED,SAASK,kBAAT,CAA2BC,MAA3B,EAAmCC,QAAnC,SAAwD;EAAA,IAATR,KAAS,SAATA,KAAS;EACvD,IAAMe,YAAY,GAAGvB,gBAAgB,CAACe,MAAD,CAArC,CADuD,CAEvD;EACA;EACA;EACA;EACA;EACA;;EACA,IAAIS,cAAJ;;EACA,IAAIR,QAAQ,KAAK,KAAjB,EAAwB;IACvBQ,cAAc,GAAGxB,gBAAgB,CAACe,MAAD,CAAjC;EACA,CAFD,MAEO;IACNS,cAAc,GAAGxB,gBAAgB,CAACgB,QAAD,CAAjC;EACA;;EACD,IAAIO,YAAY,KAAKX,SAAjB,IAA8BY,cAAc,KAAKZ,SAArD,EAAgE;IAC/D,OAAOW,YAAY,GAAGC,cAAc,IAAI,IAAIvB,+BAA+B,CAACO,KAAD,CAAvC,CAApC;EACA;AACD"}