{"version": 3, "file": "round.test.js", "names": ["getStep", "steps", "describe", "it", "getStepFor", "secondsPassed", "round", "units", "expect", "formatAs", "to", "equal"], "sources": ["../../source/steps/round.test.js"], "sourcesContent": ["import getStep from './getStep.js'\r\nimport steps from './round.js'\r\n\r\ndescribe('steps/round', () => {\r\n\tit('should get step correctly (round: \"floor\")', () => {\r\n\t\tconst getStepFor = (secondsPassed) => getStep(steps, secondsPassed, {\r\n\t\t\tround: 'floor',\r\n\t\t\tunits: [\r\n\t\t\t\t'now',\r\n\t\t\t\t'second',\r\n\t\t\t\t'minute',\r\n\t\t\t\t'hour',\r\n\t\t\t\t'day',\r\n\t\t\t\t'week',\r\n\t\t\t\t'month',\r\n\t\t\t\t'year'\r\n\t\t\t]\r\n\t\t})\r\n\r\n\t\texpect(getStepFor(0).formatAs).to.equal('now')\r\n\t\texpect(getStepFor(0.9).formatAs).to.equal('now')\r\n\t\texpect(getStepFor(1).formatAs).to.equal('second')\r\n\t\texpect(getStepFor(59.9).formatAs).to.equal('second')\r\n\t\texpect(getStepFor(60).formatAs).to.equal('minute')\r\n\t\texpect(getStepFor(60 * 60 - 1).formatAs).to.equal('minute')\r\n\t\texpect(getStepFor(60 * 60).formatAs).to.equal('hour')\r\n\t\texpect(getStepFor(24 * 60 * 60).formatAs).to.equal('day')\r\n\t\texpect(getStepFor(7 * 24 * 60 * 60).formatAs).to.equal('week')\r\n\t})\r\n\r\n\tit('should get step correctly (round: \"round\")', () => {\r\n\t\tconst getStepFor = (secondsPassed) => getStep(steps, secondsPassed, {\r\n\t\t\tround: 'round',\r\n\t\t\tunits: [\r\n\t\t\t\t'now',\r\n\t\t\t\t'second',\r\n\t\t\t\t'minute',\r\n\t\t\t\t'hour',\r\n\t\t\t\t'day',\r\n\t\t\t\t'week',\r\n\t\t\t\t'month',\r\n\t\t\t\t'year'\r\n\t\t\t]\r\n\t\t})\r\n\r\n\t\texpect(getStepFor(0).formatAs).to.equal('now')\r\n\t\texpect(getStepFor(0.49).formatAs).to.equal('now')\r\n\t\texpect(getStepFor(0.5).formatAs).to.equal('second')\r\n\t\texpect(getStepFor(1).formatAs).to.equal('second')\r\n\t\texpect(getStepFor(59.4).formatAs).to.equal('second')\r\n\t\texpect(getStepFor(60).formatAs).to.equal('minute')\r\n\t\texpect(getStepFor(59.4 * 60).formatAs).to.equal('minute')\r\n\t\texpect(getStepFor(60 * 60).formatAs).to.equal('hour')\r\n\t\texpect(getStepFor(23.49 * 60 * 60).formatAs).to.equal('hour')\r\n\t\texpect(getStepFor(23.5 * 60 * 60).formatAs).to.equal('day')\r\n\t\texpect(getStepFor(7 * 24 * 60 * 60).formatAs).to.equal('week')\r\n\t})\r\n\r\n\tit('should use \"day\"s when \"week\"s are not allowed', () => {\r\n\t\tconst getStepFor = (secondsPassed) => getStep(steps, secondsPassed, {\r\n\t\t\tunits: [\r\n\t\t\t\t'now',\r\n\t\t\t\t'second',\r\n\t\t\t\t'minute',\r\n\t\t\t\t'hour',\r\n\t\t\t\t'day',\r\n\t\t\t\t'month',\r\n\t\t\t\t'year'\r\n\t\t\t]\r\n\t\t})\r\n\r\n\t\texpect(getStepFor(7 * 24 * 60 * 60).formatAs).to.equal('day')\r\n\t})\r\n})"], "mappings": "AAAA,OAAOA,OAAP,MAAoB,cAApB;AACA,OAAOC,KAAP,MAAkB,YAAlB;AAEAC,QAAQ,CAAC,aAAD,EAAgB,YAAM;EAC7BC,EAAE,CAAC,4CAAD,EAA+C,YAAM;IACtD,IAAMC,UAAU,GAAG,SAAbA,UAAa,CAACC,aAAD;MAAA,OAAmBL,OAAO,CAACC,KAAD,EAAQI,aAAR,EAAuB;QACnEC,KAAK,EAAE,OAD4D;QAEnEC,KAAK,EAAE,CACN,KADM,EAEN,QAFM,EAGN,QAHM,EAIN,MAJM,EAKN,KALM,EAMN,MANM,EAON,OAPM,EAQN,MARM;MAF4D,CAAvB,CAA1B;IAAA,CAAnB;;IAcAC,MAAM,CAACJ,UAAU,CAAC,CAAD,CAAV,CAAcK,QAAf,CAAN,CAA+BC,EAA/B,CAAkCC,KAAlC,CAAwC,KAAxC;IACAH,MAAM,CAACJ,UAAU,CAAC,GAAD,CAAV,CAAgBK,QAAjB,CAAN,CAAiCC,EAAjC,CAAoCC,KAApC,CAA0C,KAA1C;IACAH,MAAM,CAACJ,UAAU,CAAC,CAAD,CAAV,CAAcK,QAAf,CAAN,CAA+BC,EAA/B,CAAkCC,KAAlC,CAAwC,QAAxC;IACAH,MAAM,CAACJ,UAAU,CAAC,IAAD,CAAV,CAAiBK,QAAlB,CAAN,CAAkCC,EAAlC,CAAqCC,KAArC,CAA2C,QAA3C;IACAH,MAAM,CAACJ,UAAU,CAAC,EAAD,CAAV,CAAeK,QAAhB,CAAN,CAAgCC,EAAhC,CAAmCC,KAAnC,CAAyC,QAAzC;IACAH,MAAM,CAACJ,UAAU,CAAC,KAAK,EAAL,GAAU,CAAX,CAAV,CAAwBK,QAAzB,CAAN,CAAyCC,EAAzC,CAA4CC,KAA5C,CAAkD,QAAlD;IACAH,MAAM,CAACJ,UAAU,CAAC,KAAK,EAAN,CAAV,CAAoBK,QAArB,CAAN,CAAqCC,EAArC,CAAwCC,KAAxC,CAA8C,MAA9C;IACAH,MAAM,CAACJ,UAAU,CAAC,KAAK,EAAL,GAAU,EAAX,CAAV,CAAyBK,QAA1B,CAAN,CAA0CC,EAA1C,CAA6CC,KAA7C,CAAmD,KAAnD;IACAH,MAAM,CAACJ,UAAU,CAAC,IAAI,EAAJ,GAAS,EAAT,GAAc,EAAf,CAAV,CAA6BK,QAA9B,CAAN,CAA8CC,EAA9C,CAAiDC,KAAjD,CAAuD,MAAvD;EACA,CAxBC,CAAF;EA0BAR,EAAE,CAAC,4CAAD,EAA+C,YAAM;IACtD,IAAMC,UAAU,GAAG,SAAbA,UAAa,CAACC,aAAD;MAAA,OAAmBL,OAAO,CAACC,KAAD,EAAQI,aAAR,EAAuB;QACnEC,KAAK,EAAE,OAD4D;QAEnEC,KAAK,EAAE,CACN,KADM,EAEN,QAFM,EAGN,QAHM,EAIN,MAJM,EAKN,KALM,EAMN,MANM,EAON,OAPM,EAQN,MARM;MAF4D,CAAvB,CAA1B;IAAA,CAAnB;;IAcAC,MAAM,CAACJ,UAAU,CAAC,CAAD,CAAV,CAAcK,QAAf,CAAN,CAA+BC,EAA/B,CAAkCC,KAAlC,CAAwC,KAAxC;IACAH,MAAM,CAACJ,UAAU,CAAC,IAAD,CAAV,CAAiBK,QAAlB,CAAN,CAAkCC,EAAlC,CAAqCC,KAArC,CAA2C,KAA3C;IACAH,MAAM,CAACJ,UAAU,CAAC,GAAD,CAAV,CAAgBK,QAAjB,CAAN,CAAiCC,EAAjC,CAAoCC,KAApC,CAA0C,QAA1C;IACAH,MAAM,CAACJ,UAAU,CAAC,CAAD,CAAV,CAAcK,QAAf,CAAN,CAA+BC,EAA/B,CAAkCC,KAAlC,CAAwC,QAAxC;IACAH,MAAM,CAACJ,UAAU,CAAC,IAAD,CAAV,CAAiBK,QAAlB,CAAN,CAAkCC,EAAlC,CAAqCC,KAArC,CAA2C,QAA3C;IACAH,MAAM,CAACJ,UAAU,CAAC,EAAD,CAAV,CAAeK,QAAhB,CAAN,CAAgCC,EAAhC,CAAmCC,KAAnC,CAAyC,QAAzC;IACAH,MAAM,CAACJ,UAAU,CAAC,OAAO,EAAR,CAAV,CAAsBK,QAAvB,CAAN,CAAuCC,EAAvC,CAA0CC,KAA1C,CAAgD,QAAhD;IACAH,MAAM,CAACJ,UAAU,CAAC,KAAK,EAAN,CAAV,CAAoBK,QAArB,CAAN,CAAqCC,EAArC,CAAwCC,KAAxC,CAA8C,MAA9C;IACAH,MAAM,CAACJ,UAAU,CAAC,QAAQ,EAAR,GAAa,EAAd,CAAV,CAA4BK,QAA7B,CAAN,CAA6CC,EAA7C,CAAgDC,KAAhD,CAAsD,MAAtD;IACAH,MAAM,CAACJ,UAAU,CAAC,OAAO,EAAP,GAAY,EAAb,CAAV,CAA2BK,QAA5B,CAAN,CAA4CC,EAA5C,CAA+CC,KAA/C,CAAqD,KAArD;IACAH,MAAM,CAACJ,UAAU,CAAC,IAAI,EAAJ,GAAS,EAAT,GAAc,EAAf,CAAV,CAA6BK,QAA9B,CAAN,CAA8CC,EAA9C,CAAiDC,KAAjD,CAAuD,MAAvD;EACA,CA1BC,CAAF;EA4BAR,EAAE,CAAC,gDAAD,EAAmD,YAAM;IAC1D,IAAMC,UAAU,GAAG,SAAbA,UAAa,CAACC,aAAD;MAAA,OAAmBL,OAAO,CAACC,KAAD,EAAQI,aAAR,EAAuB;QACnEE,KAAK,EAAE,CACN,KADM,EAEN,QAFM,EAGN,QAHM,EAIN,MAJM,EAKN,KALM,EAMN,OANM,EAON,MAPM;MAD4D,CAAvB,CAA1B;IAAA,CAAnB;;IAYAC,MAAM,CAACJ,UAAU,CAAC,IAAI,EAAJ,GAAS,EAAT,GAAc,EAAf,CAAV,CAA6BK,QAA9B,CAAN,CAA8CC,EAA9C,CAAiDC,KAAjD,CAAuD,KAAvD;EACA,CAdC,CAAF;AAeA,CAtEO,CAAR"}