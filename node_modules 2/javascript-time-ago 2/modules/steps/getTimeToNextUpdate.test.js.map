{"version": 3, "file": "getTimeToNextUpdate.test.js", "names": ["getTimeToNextUpdate", "INFINITY", "getStepChangesAt", "getTimeToStepChange", "describe", "it", "expect", "minTime", "format", "now", "future", "isFirstStep", "to", "equal", "Date", "nextStep", "formatAs", "be", "undefined", "should", "test", "unit", "prevStep"], "sources": ["../../source/steps/getTimeToNextUpdate.test.js"], "sourcesContent": ["import getTimeToNextUpdate, { INFINITY, getStepChangesAt, getTimeToStepChange } from './getTimeToNextUpdate.js'\r\n\r\ndescribe('getTimeToNextUpdate', () => {\r\n\tit('should return infinity when there are no more steps, and it does not format as a unit (past)', () => {\r\n\t\texpect(getTimeToNextUpdate(-4 * 60 * 1000, {\r\n\t\t\tminTime: 59.5,\r\n\t\t\tformat: () => ''\r\n\t\t}, {\r\n\t\t\tnow: 0,\r\n\t\t\tfuture: false,\r\n\t\t\tisFirstStep: true\r\n\t\t})).to.equal(INFINITY)\r\n\t})\r\n\r\n\tit('should support date argument', () => {\r\n\t\texpect(getTimeToNextUpdate(new Date(4 * 60 * 1000), {\r\n\t\t\tminTime: 60\r\n\t\t}, {\r\n\t\t\tnow: 0,\r\n\t\t\tfuture: true,\r\n\t\t\tisFirstStep: true,\r\n\t\t\tnextStep: {}\r\n\t\t})).to.equal(3 * 60 * 1000 + 1)\r\n\t})\r\n\r\n\tit('should return this step\\'s \"minTime\" timestamp (future)', () => {\r\n\t\texpect(getTimeToNextUpdate(4 * 60 * 1000, {\r\n\t\t\tminTime: 60,\r\n\t\t\tformat: () => ''\r\n\t\t}, {\r\n\t\t\tnow: 0,\r\n\t\t\tfuture: true,\r\n\t\t\tisFirstStep: true,\r\n\t\t\tnextStep: {\r\n\t\t\t\tformat: () => ''\r\n\t\t\t}\r\n\t\t})).to.equal(3 * 60 * 1000 + 1)\r\n\t})\r\n\r\n\tit('should return undefined when there is a next step and time to next update can not be reliably determined (formatAs) (past)', () => {\r\n\t\texpect(getTimeToNextUpdate(-4 * 60 * 1000, {\r\n\t\t\tminTime: 60,\r\n\t\t\tformatAs: 'minute'\r\n\t\t}, {\r\n\t\t\tnow: 0,\r\n\t\t\tfuture: false,\r\n\t\t\tisFirstStep: true,\r\n\t\t\tnextStep: {\r\n\t\t\t\tformatAs: 'unknown-time-unit'\r\n\t\t\t}\r\n\t\t})).to.be.undefined\r\n\t})\r\n\r\n\tit('should get time to next update (no next step) (past)', () => {\r\n\t\tgetTimeToNextUpdate(-4 * 60 * 1000, {\r\n\t\t\tformatAs: 'minute',\r\n\t\t\tminTime: 59.5\r\n\t\t}, {\r\n\t\t\tnow: 0,\r\n\t\t\tfuture: false,\r\n\t\t\tisFirstStep: true\r\n\t\t}).should.equal(0.5 * 60 * 1000)\r\n\t})\r\n\r\n\tit('should get time to next update (no next step) (future)', () => {\r\n\t\tgetTimeToNextUpdate(4 * 60 * 1000, {\r\n\t\t\tformatAs: 'minute',\r\n\t\t\tminTime: 59.5\r\n\t\t}, {\r\n\t\t\tnow: 0,\r\n\t\t\tfuture: true,\r\n\t\t\tisFirstStep: true\r\n\t\t}).should.equal(0.5 * 60 * 1000 + 1)\r\n\t})\r\n\r\n\tit('should get time to next update (has prev/next step without `minTime`) (future)', () => {\r\n\t\tgetTimeToNextUpdate(4 * 60 * 1000, {\r\n\t\t\tformatAs: 'minute',\r\n\t\t\tminTime: 59.5\r\n\t\t}, {\r\n\t\t\tnow: 0,\r\n\t\t\tfuture: true,\r\n\t\t\tisFirstStep: true,\r\n\t\t\tnextStep: {\r\n\t\t\t\tformatAs: 'hour',\r\n\t\t\t\ttest: () => false\r\n\t\t\t}\r\n\t\t}).should.equal(0.5 * 60 * 1000 + 1)\r\n\t})\r\n\r\n\tit('should get time to next update (has `getTimeToNextUpdate`) (past)', () => {\r\n\t\tgetTimeToNextUpdate(-4 * 60 * 1000, {\r\n\t\t\tformatAs: 'minute',\r\n\t\t\tminTime: 59.5,\r\n\t\t\tgetTimeToNextUpdate: () => 0.25 * 60 * 1000\r\n\t\t}, {\r\n\t\t\tnow: 0,\r\n\t\t\tfuture: false,\r\n\t\t\tisFirstStep: true\r\n\t\t}).should.equal(0.25 * 60 * 1000)\r\n\t})\r\n\r\n\tit('should get time to next update (has `getTimeToNextUpdate`) (future)', () => {\r\n\t\tgetTimeToNextUpdate(4 * 60 * 1000, {\r\n\t\t\tformatAs: 'minute',\r\n\t\t\tminTime: 59.5,\r\n\t\t\tgetTimeToNextUpdate: () => 0.25 * 60 * 1000\r\n\t\t}, {\r\n\t\t\tnow: 0,\r\n\t\t\tfuture: true,\r\n\t\t\tisFirstStep: true\r\n\t\t}).should.equal(0.25 * 60 * 1000)\r\n\t})\r\n\r\n\tit('should get time to next update (has both unit and prev/next steps with `minTime`) (returns time to \"minTime\" of next step) (past)', () => {\r\n\t\tgetTimeToNextUpdate(-59 * 60 * 1000, {\r\n\t\t\tformatAs: 'minute',\r\n\t\t\tminTime: 59.5\r\n\t\t}, {\r\n\t\t\tnow: 0,\r\n\t\t\tfuture: false,\r\n\t\t\tisFirstStep: true,\r\n\t\t\tnextStep: {\r\n\t\t\t\tformatAs: 'hour',\r\n\t\t\t\tminTime: 59.5 * 60\r\n\t\t\t}\r\n\t\t}).should.equal(0.5 * 60 * 1000)\r\n\t})\r\n\r\n\tit('should get time to next update (has no unit but has prev/next step with `minTime`) (returns time to \"minTime\" of next step) (past)', () => {\r\n\t\tgetTimeToNextUpdate(-59 * 60 * 1000, {\r\n\t\t\tformat: () => {},\r\n\t\t\tminTime: 59.5\r\n\t\t}, {\r\n\t\t\tnow: 0,\r\n\t\t\tfuture: false,\r\n\t\t\tisFirstStep: true,\r\n\t\t\tnextStep: {\r\n\t\t\t\tformatAs: 'hour',\r\n\t\t\t\tminTime: 59.5 * 60\r\n\t\t\t}\r\n\t\t}).should.equal(0.5 * 60 * 1000)\r\n\t})\r\n\r\n\tit('should get time to next update (will be outside of the first step) (future)', () => {\r\n\t\tgetTimeToNextUpdate(60 * 60 * 1000, {\r\n\t\t\tformatAs: 'hour',\r\n\t\t\tminTime: 60 * 60\r\n\t\t}, {\r\n\t\t\tnow: 0,\r\n\t\t\tfuture: true,\r\n\t\t\tisFirstStep: true\r\n\t\t}).should.equal(1)\r\n\t})\r\n})\r\n\r\ndescribe('getStepChangesAt', () => {\r\n\tit('should work for \"round\" steps', () => {\r\n\t\t// Future.\r\n\t\t// Is at zero point.\r\n\t\t// No next step.\r\n\t\t// No tickable unit.\r\n\t\t// Doesn't update.\r\n\t\tgetStepChangesAt({\r\n\t\t\tunit: 'now'\r\n\t\t}, 0, {\r\n\t\t\tnow: 0,\r\n\t\t\tfuture: false,\r\n\t\t\tprevStep: undefined\r\n\t\t}).should.equal(INFINITY)\r\n\r\n\t\t// Past.\r\n\t\t// Is at zero point.\r\n\t\t// The next step is seconds.\r\n\t\t// Updates at the next step.\r\n\t\tgetStepChangesAt({\r\n\t\t\tunit: 'second',\r\n\t\t\tminTime: 1\r\n\t\t}, 0, {\r\n\t\t\tnow: 0,\r\n\t\t\tfuture: false,\r\n\t\t\tprevStep: {}\r\n\t\t}).should.equal(1 * 1000)\r\n\r\n\t\t// Future.\r\n\t\t// Inside the first step.\r\n\t\t// Updates after zero point.\r\n\t\tgetStepChangesAt({\r\n\t\t\tunit: 'now'\r\n\t\t}, 0.9 * 1000, {\r\n\t\t\tnow: 0,\r\n\t\t\tfuture: true,\r\n\t\t\tprevStep: undefined\r\n\t\t}).should.equal(0.9 * 1000 + 1)\r\n\r\n\t\t// Future.\r\n\t\t// The first step doesn't start at 0.\r\n\t\t// Outside of the first step.\r\n\t\t// Updates right after zero point.\r\n\t\tgetTimeToStepChange(undefined, 0.9 * 1000, {\r\n\t\t\tnow: 0,\r\n\t\t\tfuture: true,\r\n\t\t\tprevStep: undefined\r\n\t\t}).should.equal(0.9 * 1000 + 1)\r\n\r\n\t\t// Past.\r\n\t\t// The current step is `undefined`.\r\n\t\t// The next step is the first step.\r\n\t\t// The first step doesn't start at 0.\r\n\t\t// Outside of the first step.\r\n\t\t// Updates at entering the first step.\r\n\t\tgetStepChangesAt({\r\n\t\t\tminTime: 1,\r\n\t\t\tunit: 'second'\r\n\t\t}, -0.9 * 1000, {\r\n\t\t\tnow: 0,\r\n\t\t\tfuture: false,\r\n\t\t\tprevStep: {}\r\n\t\t}).should.equal(0.1 * 1000)\r\n\r\n\t\t// Future.\r\n\t\t// The first step doesn't start at 0.\r\n\t\t// Will output empty string after it exits the current step.\r\n\t\tgetStepChangesAt({\r\n\t\t\tminTime: 1,\r\n\t\t\tunit: 'second'\r\n\t\t}, 1.1 * 1000, {\r\n\t\t\tnow: 0,\r\n\t\t\tfuture: true,\r\n\t\t\tprevStep: undefined\r\n\t\t}).should.equal(0.1 * 1000 + 1)\r\n\r\n\t\t// Past.\r\n\t\t// Next step is seconds.\r\n\t\t// The \"next\" step doesn't have `minTime`,\r\n\t\t// so \"time to next update\" couldn't be determined.\r\n\t\texpect(getStepChangesAt({\r\n\t\t\tunit: 'unknown-time-unit'\r\n\t\t}, 0, {\r\n\t\t\tnow: 0,\r\n\t\t\tfuture: false,\r\n\t\t\tprevStep: {}\r\n\t\t})).to.be.undefined\r\n\r\n\t\t// Past.\r\n\t\t// No next step.\r\n\t\t// The last step never changes.\r\n\t\tgetTimeToStepChange(undefined, 0, {\r\n\t\t\tnow: 0,\r\n\t\t\tfuture: false,\r\n\t\t\tisFirstStep: undefined\r\n\t\t}).should.equal(INFINITY)\r\n\r\n\t\t// Future.\r\n\t\t// Current step is seconds.\r\n\t\t// Updates after zero point.\r\n\t\tgetStepChangesAt({\r\n\t\t\tunit: 'second'\r\n\t\t}, 0, {\r\n\t\t\tnow: 0,\r\n\t\t\tfuture: true,\r\n\t\t\tprevStep: undefined\r\n\t\t}).should.equal(1)\r\n\r\n\t\t// Past.\r\n\t\t// Next step is minutes.\r\n\t\t// Already at zero point, so no need to update at zero point.\r\n\t\tgetStepChangesAt({\r\n\t\t\tminTime: 60,\r\n\t\t\tformatAs: 'minute'\r\n\t\t}, 0, {\r\n\t\t\tnow: 0,\r\n\t\t\tfuture: false,\r\n\t\t\tprevStep: {}\r\n\t\t}).should.equal(60 * 1000)\r\n\t})\r\n})"], "mappings": "AAAA,OAAOA,mBAAP,IAA8BC,QAA9B,EAAwCC,gBAAxC,EAA0DC,mBAA1D,QAAqF,0BAArF;AAEAC,QAAQ,CAAC,qBAAD,EAAwB,YAAM;EACrCC,EAAE,CAAC,8FAAD,EAAiG,YAAM;IACxGC,MAAM,CAACN,mBAAmB,CAAC,CAAC,CAAD,GAAK,EAAL,GAAU,IAAX,EAAiB;MAC1CO,OAAO,EAAE,IADiC;MAE1CC,MAAM,EAAE;QAAA,OAAM,EAAN;MAAA;IAFkC,CAAjB,EAGvB;MACFC,GAAG,EAAE,CADH;MAEFC,MAAM,EAAE,KAFN;MAGFC,WAAW,EAAE;IAHX,CAHuB,CAApB,CAAN,CAOIC,EAPJ,CAOOC,KAPP,CAOaZ,QAPb;EAQA,CATC,CAAF;EAWAI,EAAE,CAAC,8BAAD,EAAiC,YAAM;IACxCC,MAAM,CAACN,mBAAmB,CAAC,IAAIc,IAAJ,CAAS,IAAI,EAAJ,GAAS,IAAlB,CAAD,EAA0B;MACnDP,OAAO,EAAE;IAD0C,CAA1B,EAEvB;MACFE,GAAG,EAAE,CADH;MAEFC,MAAM,EAAE,IAFN;MAGFC,WAAW,EAAE,IAHX;MAIFI,QAAQ,EAAE;IAJR,CAFuB,CAApB,CAAN,CAOIH,EAPJ,CAOOC,KAPP,CAOa,IAAI,EAAJ,GAAS,IAAT,GAAgB,CAP7B;EAQA,CATC,CAAF;EAWAR,EAAE,CAAC,yDAAD,EAA4D,YAAM;IACnEC,MAAM,CAACN,mBAAmB,CAAC,IAAI,EAAJ,GAAS,IAAV,EAAgB;MACzCO,OAAO,EAAE,EADgC;MAEzCC,MAAM,EAAE;QAAA,OAAM,EAAN;MAAA;IAFiC,CAAhB,EAGvB;MACFC,GAAG,EAAE,CADH;MAEFC,MAAM,EAAE,IAFN;MAGFC,WAAW,EAAE,IAHX;MAIFI,QAAQ,EAAE;QACTP,MAAM,EAAE;UAAA,OAAM,EAAN;QAAA;MADC;IAJR,CAHuB,CAApB,CAAN,CAUII,EAVJ,CAUOC,KAVP,CAUa,IAAI,EAAJ,GAAS,IAAT,GAAgB,CAV7B;EAWA,CAZC,CAAF;EAcAR,EAAE,CAAC,4HAAD,EAA+H,YAAM;IACtIC,MAAM,CAACN,mBAAmB,CAAC,CAAC,CAAD,GAAK,EAAL,GAAU,IAAX,EAAiB;MAC1CO,OAAO,EAAE,EADiC;MAE1CS,QAAQ,EAAE;IAFgC,CAAjB,EAGvB;MACFP,GAAG,EAAE,CADH;MAEFC,MAAM,EAAE,KAFN;MAGFC,WAAW,EAAE,IAHX;MAIFI,QAAQ,EAAE;QACTC,QAAQ,EAAE;MADD;IAJR,CAHuB,CAApB,CAAN,CAUIJ,EAVJ,CAUOK,EAVP,CAUUC,SAVV;EAWA,CAZC,CAAF;EAcAb,EAAE,CAAC,sDAAD,EAAyD,YAAM;IAChEL,mBAAmB,CAAC,CAAC,CAAD,GAAK,EAAL,GAAU,IAAX,EAAiB;MACnCgB,QAAQ,EAAE,QADyB;MAEnCT,OAAO,EAAE;IAF0B,CAAjB,EAGhB;MACFE,GAAG,EAAE,CADH;MAEFC,MAAM,EAAE,KAFN;MAGFC,WAAW,EAAE;IAHX,CAHgB,CAAnB,CAOGQ,MAPH,CAOUN,KAPV,CAOgB,MAAM,EAAN,GAAW,IAP3B;EAQA,CATC,CAAF;EAWAR,EAAE,CAAC,wDAAD,EAA2D,YAAM;IAClEL,mBAAmB,CAAC,IAAI,EAAJ,GAAS,IAAV,EAAgB;MAClCgB,QAAQ,EAAE,QADwB;MAElCT,OAAO,EAAE;IAFyB,CAAhB,EAGhB;MACFE,GAAG,EAAE,CADH;MAEFC,MAAM,EAAE,IAFN;MAGFC,WAAW,EAAE;IAHX,CAHgB,CAAnB,CAOGQ,MAPH,CAOUN,KAPV,CAOgB,MAAM,EAAN,GAAW,IAAX,GAAkB,CAPlC;EAQA,CATC,CAAF;EAWAR,EAAE,CAAC,gFAAD,EAAmF,YAAM;IAC1FL,mBAAmB,CAAC,IAAI,EAAJ,GAAS,IAAV,EAAgB;MAClCgB,QAAQ,EAAE,QADwB;MAElCT,OAAO,EAAE;IAFyB,CAAhB,EAGhB;MACFE,GAAG,EAAE,CADH;MAEFC,MAAM,EAAE,IAFN;MAGFC,WAAW,EAAE,IAHX;MAIFI,QAAQ,EAAE;QACTC,QAAQ,EAAE,MADD;QAETI,IAAI,EAAE;UAAA,OAAM,KAAN;QAAA;MAFG;IAJR,CAHgB,CAAnB,CAWGD,MAXH,CAWUN,KAXV,CAWgB,MAAM,EAAN,GAAW,IAAX,GAAkB,CAXlC;EAYA,CAbC,CAAF;EAeAR,EAAE,CAAC,mEAAD,EAAsE,YAAM;IAC7EL,mBAAmB,CAAC,CAAC,CAAD,GAAK,EAAL,GAAU,IAAX,EAAiB;MACnCgB,QAAQ,EAAE,QADyB;MAEnCT,OAAO,EAAE,IAF0B;MAGnCP,mBAAmB,EAAE;QAAA,OAAM,OAAO,EAAP,GAAY,IAAlB;MAAA;IAHc,CAAjB,EAIhB;MACFS,GAAG,EAAE,CADH;MAEFC,MAAM,EAAE,KAFN;MAGFC,WAAW,EAAE;IAHX,CAJgB,CAAnB,CAQGQ,MARH,CAQUN,KARV,CAQgB,OAAO,EAAP,GAAY,IAR5B;EASA,CAVC,CAAF;EAYAR,EAAE,CAAC,qEAAD,EAAwE,YAAM;IAC/EL,mBAAmB,CAAC,IAAI,EAAJ,GAAS,IAAV,EAAgB;MAClCgB,QAAQ,EAAE,QADwB;MAElCT,OAAO,EAAE,IAFyB;MAGlCP,mBAAmB,EAAE;QAAA,OAAM,OAAO,EAAP,GAAY,IAAlB;MAAA;IAHa,CAAhB,EAIhB;MACFS,GAAG,EAAE,CADH;MAEFC,MAAM,EAAE,IAFN;MAGFC,WAAW,EAAE;IAHX,CAJgB,CAAnB,CAQGQ,MARH,CAQUN,KARV,CAQgB,OAAO,EAAP,GAAY,IAR5B;EASA,CAVC,CAAF;EAYAR,EAAE,CAAC,mIAAD,EAAsI,YAAM;IAC7IL,mBAAmB,CAAC,CAAC,EAAD,GAAM,EAAN,GAAW,IAAZ,EAAkB;MACpCgB,QAAQ,EAAE,QAD0B;MAEpCT,OAAO,EAAE;IAF2B,CAAlB,EAGhB;MACFE,GAAG,EAAE,CADH;MAEFC,MAAM,EAAE,KAFN;MAGFC,WAAW,EAAE,IAHX;MAIFI,QAAQ,EAAE;QACTC,QAAQ,EAAE,MADD;QAETT,OAAO,EAAE,OAAO;MAFP;IAJR,CAHgB,CAAnB,CAWGY,MAXH,CAWUN,KAXV,CAWgB,MAAM,EAAN,GAAW,IAX3B;EAYA,CAbC,CAAF;EAeAR,EAAE,CAAC,oIAAD,EAAuI,YAAM;IAC9IL,mBAAmB,CAAC,CAAC,EAAD,GAAM,EAAN,GAAW,IAAZ,EAAkB;MACpCQ,MAAM,EAAE,kBAAM,CAAE,CADoB;MAEpCD,OAAO,EAAE;IAF2B,CAAlB,EAGhB;MACFE,GAAG,EAAE,CADH;MAEFC,MAAM,EAAE,KAFN;MAGFC,WAAW,EAAE,IAHX;MAIFI,QAAQ,EAAE;QACTC,QAAQ,EAAE,MADD;QAETT,OAAO,EAAE,OAAO;MAFP;IAJR,CAHgB,CAAnB,CAWGY,MAXH,CAWUN,KAXV,CAWgB,MAAM,EAAN,GAAW,IAX3B;EAYA,CAbC,CAAF;EAeAR,EAAE,CAAC,6EAAD,EAAgF,YAAM;IACvFL,mBAAmB,CAAC,KAAK,EAAL,GAAU,IAAX,EAAiB;MACnCgB,QAAQ,EAAE,MADyB;MAEnCT,OAAO,EAAE,KAAK;IAFqB,CAAjB,EAGhB;MACFE,GAAG,EAAE,CADH;MAEFC,MAAM,EAAE,IAFN;MAGFC,WAAW,EAAE;IAHX,CAHgB,CAAnB,CAOGQ,MAPH,CAOUN,KAPV,CAOgB,CAPhB;EAQA,CATC,CAAF;AAUA,CAxJO,CAAR;AA0JAT,QAAQ,CAAC,kBAAD,EAAqB,YAAM;EAClCC,EAAE,CAAC,+BAAD,EAAkC,YAAM;IACzC;IACA;IACA;IACA;IACA;IACAH,gBAAgB,CAAC;MAChBmB,IAAI,EAAE;IADU,CAAD,EAEb,CAFa,EAEV;MACLZ,GAAG,EAAE,CADA;MAELC,MAAM,EAAE,KAFH;MAGLY,QAAQ,EAAEJ;IAHL,CAFU,CAAhB,CAMGC,MANH,CAMUN,KANV,CAMgBZ,QANhB,EANyC,CAczC;IACA;IACA;IACA;;IACAC,gBAAgB,CAAC;MAChBmB,IAAI,EAAE,QADU;MAEhBd,OAAO,EAAE;IAFO,CAAD,EAGb,CAHa,EAGV;MACLE,GAAG,EAAE,CADA;MAELC,MAAM,EAAE,KAFH;MAGLY,QAAQ,EAAE;IAHL,CAHU,CAAhB,CAOGH,MAPH,CAOUN,KAPV,CAOgB,IAAI,IAPpB,EAlByC,CA2BzC;IACA;IACA;;IACAX,gBAAgB,CAAC;MAChBmB,IAAI,EAAE;IADU,CAAD,EAEb,MAAM,IAFO,EAED;MACdZ,GAAG,EAAE,CADS;MAEdC,MAAM,EAAE,IAFM;MAGdY,QAAQ,EAAEJ;IAHI,CAFC,CAAhB,CAMGC,MANH,CAMUN,KANV,CAMgB,MAAM,IAAN,GAAa,CAN7B,EA9ByC,CAsCzC;IACA;IACA;IACA;;IACAV,mBAAmB,CAACe,SAAD,EAAY,MAAM,IAAlB,EAAwB;MAC1CT,GAAG,EAAE,CADqC;MAE1CC,MAAM,EAAE,IAFkC;MAG1CY,QAAQ,EAAEJ;IAHgC,CAAxB,CAAnB,CAIGC,MAJH,CAIUN,KAJV,CAIgB,MAAM,IAAN,GAAa,CAJ7B,EA1CyC,CAgDzC;IACA;IACA;IACA;IACA;IACA;;IACAX,gBAAgB,CAAC;MAChBK,OAAO,EAAE,CADO;MAEhBc,IAAI,EAAE;IAFU,CAAD,EAGb,CAAC,GAAD,GAAO,IAHM,EAGA;MACfZ,GAAG,EAAE,CADU;MAEfC,MAAM,EAAE,KAFO;MAGfY,QAAQ,EAAE;IAHK,CAHA,CAAhB,CAOGH,MAPH,CAOUN,KAPV,CAOgB,MAAM,IAPtB,EAtDyC,CA+DzC;IACA;IACA;;IACAX,gBAAgB,CAAC;MAChBK,OAAO,EAAE,CADO;MAEhBc,IAAI,EAAE;IAFU,CAAD,EAGb,MAAM,IAHO,EAGD;MACdZ,GAAG,EAAE,CADS;MAEdC,MAAM,EAAE,IAFM;MAGdY,QAAQ,EAAEJ;IAHI,CAHC,CAAhB,CAOGC,MAPH,CAOUN,KAPV,CAOgB,MAAM,IAAN,GAAa,CAP7B,EAlEyC,CA2EzC;IACA;IACA;IACA;;IACAP,MAAM,CAACJ,gBAAgB,CAAC;MACvBmB,IAAI,EAAE;IADiB,CAAD,EAEpB,CAFoB,EAEjB;MACLZ,GAAG,EAAE,CADA;MAELC,MAAM,EAAE,KAFH;MAGLY,QAAQ,EAAE;IAHL,CAFiB,CAAjB,CAAN,CAMIV,EANJ,CAMOK,EANP,CAMUC,SANV,CA/EyC,CAuFzC;IACA;IACA;;IACAf,mBAAmB,CAACe,SAAD,EAAY,CAAZ,EAAe;MACjCT,GAAG,EAAE,CAD4B;MAEjCC,MAAM,EAAE,KAFyB;MAGjCC,WAAW,EAAEO;IAHoB,CAAf,CAAnB,CAIGC,MAJH,CAIUN,KAJV,CAIgBZ,QAJhB,EA1FyC,CAgGzC;IACA;IACA;;IACAC,gBAAgB,CAAC;MAChBmB,IAAI,EAAE;IADU,CAAD,EAEb,CAFa,EAEV;MACLZ,GAAG,EAAE,CADA;MAELC,MAAM,EAAE,IAFH;MAGLY,QAAQ,EAAEJ;IAHL,CAFU,CAAhB,CAMGC,MANH,CAMUN,KANV,CAMgB,CANhB,EAnGyC,CA2GzC;IACA;IACA;;IACAX,gBAAgB,CAAC;MAChBK,OAAO,EAAE,EADO;MAEhBS,QAAQ,EAAE;IAFM,CAAD,EAGb,CAHa,EAGV;MACLP,GAAG,EAAE,CADA;MAELC,MAAM,EAAE,KAFH;MAGLY,QAAQ,EAAE;IAHL,CAHU,CAAhB,CAOGH,MAPH,CAOUN,KAPV,CAOgB,KAAK,IAPrB;EAQA,CAtHC,CAAF;AAuHA,CAxHO,CAAR"}