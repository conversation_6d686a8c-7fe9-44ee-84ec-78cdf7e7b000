{"version": 3, "file": "getStepMinTime.test.js", "names": ["getStepMinTime", "describe", "it", "test", "prevStep", "minTime", "should", "equal"], "sources": ["../../source/steps/getStepMinTime.test.js"], "sourcesContent": ["import getStepMinTime from './getStepMinTime.js'\r\n\r\ndescribe('getStepMinTime', () => {\r\n\tit('should support `step.test()` function (returns true)', () => {\r\n\t\tgetStepMinTime({\r\n\t\t\ttest: () => true\r\n\t\t}, {\r\n\t\t\tprevStep: { minTime: 1 }\r\n\t\t}).should.equal(0)\r\n\t})\r\n\r\n\tit('should support `step.test()` function (returns false)', () => {\r\n\t\tgetStepMinTime({\r\n\t\t\ttest: () => false\r\n\t\t}, {\r\n\t\t\tprevStep: { minTime: 1 }\r\n\t\t}).should.equal(9007199254740991)\r\n\t})\r\n})"], "mappings": "AAAA,OAAOA,cAAP,MAA2B,qBAA3B;AAEAC,QAAQ,CAAC,gBAAD,EAAmB,YAAM;EAChCC,EAAE,CAAC,sDAAD,EAAyD,YAAM;IAChEF,cAAc,CAAC;MACdG,IAAI,EAAE;QAAA,OAAM,IAAN;MAAA;IADQ,CAAD,EAEX;MACFC,QAAQ,EAAE;QAAEC,OAAO,EAAE;MAAX;IADR,CAFW,CAAd,CAIGC,MAJH,CAIUC,KAJV,CAIgB,CAJhB;EAKA,CANC,CAAF;EAQAL,EAAE,CAAC,uDAAD,EAA0D,YAAM;IACjEF,cAAc,CAAC;MACdG,IAAI,EAAE;QAAA,OAAM,KAAN;MAAA;IADQ,CAAD,EAEX;MACFC,QAAQ,EAAE;QAAEC,OAAO,EAAE;MAAX;IADR,CAFW,CAAd,CAIGC,MAJH,CAIUC,KAJV,CAIgB,gBAJhB;EAKA,CANC,CAAF;AAOA,CAhBO,CAAR"}