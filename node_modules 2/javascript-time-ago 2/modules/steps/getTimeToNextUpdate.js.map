{"version": 3, "file": "getTimeToNextUpdate.js", "names": ["_getTimeToNextUpdateForUnit", "getStepMinTime", "getRoundFunction", "YEAR", "INFINITY", "getTimeToNextUpdate", "date", "step", "prevStep", "nextStep", "now", "future", "round", "timestamp", "getTime", "getTimeToNextUpdateForUnit", "unit", "timeToStepChange", "getTimeToStepChange", "undefined", "timeToNextUpdate", "formatAs", "Math", "min", "getStepChangesAt", "currentOrNextStep", "minTime", "stepChangesAt"], "sources": ["../../source/steps/getTimeToNextUpdate.js"], "sourcesContent": ["import _getTimeToNextUpdateForUnit from './getTimeToNextUpdateForUnit.js'\r\nimport getStepMinTime from './getStepMinTime.js'\r\nimport { getRoundFunction } from '../round.js'\r\n\r\n// A thousand years is practically a metaphor for \"infinity\".\r\nconst YEAR = 365 * 24 * 60 * 60 * 1000\r\nexport const INFINITY = 1000 * YEAR\r\n\r\n/**\r\n * Gets the time to next update for a date and a step.\r\n * @param  {number} date — The date passed to `.format()`, converted to a timestamp.\r\n * @param  {object} step\r\n * @param  {object} [options.previousStep]\r\n * @param  {object} [options.nextStep]\r\n * @param  {number} options.now\r\n * @param  {boolean} options.future\r\n * @param  {string} [options.round] - (undocumented) Rounding mechanism.\r\n * @return {number} [timeToNextUpdate]\r\n */\r\nexport default function getTimeToNextUpdate(date, step, { prevStep, nextStep, now, future, round }) {\r\n\tconst timestamp = date.getTime ? date.getTime() : date\r\n\r\n\tconst getTimeToNextUpdateForUnit = (unit) => _getTimeToNextUpdateForUnit(unit, timestamp, { now, round })\r\n\r\n\t// For future dates, steps move from the last one to the first one,\r\n\t// while for past dates, steps move from the first one to the last one,\r\n\t// due to the fact that time flows in one direction,\r\n\t// and future dates' interval naturally becomes smaller\r\n\t// while past dates' interval naturally grows larger.\r\n\t//\r\n\t// For future dates, it's the transition\r\n\t// from the current step to the previous step,\r\n\t// therefore check the `minTime` of the current step.\r\n\t//\r\n\t// For past dates, it's the transition\r\n\t// from the current step to the next step,\r\n\t// therefore check the `minTime` of the next step.\r\n\t//\r\n\tconst timeToStepChange = getTimeToStepChange(future ? step : nextStep, timestamp, {\r\n\t\tfuture,\r\n\t\tnow,\r\n\t\tround,\r\n\t\tprevStep: future ? prevStep : step,\r\n\t\t// isFirstStep: future && isFirstStep\r\n\t})\r\n\r\n\tif (timeToStepChange === undefined) {\r\n\t\t// Can't reliably determine \"time to next update\"\r\n\t\t// if not all of the steps provide `minTime`.\r\n\t\treturn\r\n\t}\r\n\r\n\tlet timeToNextUpdate\r\n\r\n\tif (step) {\r\n\t\tif (step.getTimeToNextUpdate) {\r\n\t\t\ttimeToNextUpdate = step.getTimeToNextUpdate(timestamp, {\r\n\t\t\t\tgetTimeToNextUpdateForUnit,\r\n\t\t\t\tgetRoundFunction,\r\n\t\t\t\tnow,\r\n\t\t\t\tfuture,\r\n\t\t\t\tround\r\n\t\t\t})\r\n\t\t}\r\n\r\n\t\tif (timeToNextUpdate === undefined) {\r\n\t\t\t// \"unit\" is now called \"formatAs\".\r\n\t\t\tconst unit = step.unit || step.formatAs\r\n\t\t\tif (unit) {\r\n\t\t\t\t// For some units, like \"now\", there's no defined amount of seconds in them.\r\n\t\t\t\t// In such cases, `getTimeToNextUpdateForUnit()` returns `undefined`,\r\n\t\t\t\t// and the next step's `minTime` could be used to calculate the update interval:\r\n\t\t\t\t// it will just assume that the label never changes for this step.\r\n\t\t\t\ttimeToNextUpdate = getTimeToNextUpdateForUnit(unit)\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\tif (timeToNextUpdate === undefined) {\r\n\t\treturn timeToStepChange\r\n\t}\r\n\r\n\treturn Math.min(timeToNextUpdate, timeToStepChange)\r\n}\r\n\r\nexport function getStepChangesAt(currentOrNextStep, timestamp, { now, future, round, prevStep }) {\r\n\t// The first step's `minTime` is `0` by default.\r\n\t// It doesn't \"change\" steps at zero point\r\n\t// but it does change the wording when switching\r\n\t// from \"future\" to \"past\": \"in ...\" -> \"... ago\".\r\n\t// Therefore, the label should be updated at zero-point too.\r\n\tconst minTime = getStepMinTime(currentOrNextStep, { timestamp, now, future, round, prevStep })\r\n\tif (minTime === undefined) {\r\n\t\treturn\r\n\t}\r\n\tif (future) {\r\n\t\t// The step changes to the previous step\r\n\t\t// as soon as `timestamp - now` becomes\r\n\t\t// less than the `minTime` of the current step:\r\n\t\t// `timestamp - now === minTime - 1`\r\n\t\t// => `now === timestamp - minTime + 1`.\r\n\t\treturn timestamp - minTime * 1000 + 1\r\n\t} else {\r\n\t\t// The step changes to the next step\r\n\t\t// as soon as `now - timestamp` becomes\r\n\t\t// equal to `minTime` of the next step:\r\n\t\t// `now - timestamp === minTime`\r\n\t\t// => `now === timestamp + minTime`.\r\n\r\n\t\t// This is a special case when double-update could be skipped.\r\n\t\tif (minTime === 0 && timestamp === now) {\r\n\t\t\treturn INFINITY\r\n\t\t}\r\n\r\n\t\treturn timestamp + minTime * 1000\r\n\t}\r\n}\r\n\r\nexport function getTimeToStepChange(step, timestamp, {\r\n\tnow,\r\n\tfuture,\r\n\tround,\r\n\tprevStep\r\n}) {\r\n\tif (step) {\r\n\t\tconst stepChangesAt = getStepChangesAt(step, timestamp, {\r\n\t\t\tnow,\r\n\t\t\tfuture,\r\n\t\t\tround,\r\n\t\t\tprevStep\r\n\t\t})\r\n\t\tif (stepChangesAt === undefined) {\r\n\t\t\treturn\r\n\t\t}\r\n\t\treturn stepChangesAt - now\r\n\t} else {\r\n\t\tif (future) {\r\n\t\t\t// No step.\r\n\t\t\t// Update right after zero point, when it changes from \"future\" to \"past\".\r\n\t\t\treturn timestamp - now + 1\r\n\t\t} else {\r\n\t\t\t// The last step doesn't ever change when `date` is in the past.\r\n\t\t\treturn INFINITY\r\n\t\t}\r\n\t}\r\n}"], "mappings": "AAAA,OAAOA,2BAAP,MAAwC,iCAAxC;AACA,OAAOC,cAAP,MAA2B,qBAA3B;AACA,SAASC,gBAAT,QAAiC,aAAjC,C,CAEA;;AACA,IAAMC,IAAI,GAAG,MAAM,EAAN,GAAW,EAAX,GAAgB,EAAhB,GAAqB,IAAlC;AACA,OAAO,IAAMC,QAAQ,GAAG,OAAOD,IAAxB;AAEP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,eAAe,SAASE,mBAAT,CAA6BC,IAA7B,EAAmCC,IAAnC,QAAqF;EAAA,IAA1CC,QAA0C,QAA1CA,QAA0C;EAAA,IAAhCC,QAAgC,QAAhCA,QAAgC;EAAA,IAAtBC,GAAsB,QAAtBA,GAAsB;EAAA,IAAjBC,MAAiB,QAAjBA,MAAiB;EAAA,IAATC,KAAS,QAATA,KAAS;EACnG,IAAMC,SAAS,GAAGP,IAAI,CAACQ,OAAL,GAAeR,IAAI,CAACQ,OAAL,EAAf,GAAgCR,IAAlD;;EAEA,IAAMS,0BAA0B,GAAG,SAA7BA,0BAA6B,CAACC,IAAD;IAAA,OAAUhB,2BAA2B,CAACgB,IAAD,EAAOH,SAAP,EAAkB;MAAEH,GAAG,EAAHA,GAAF;MAAOE,KAAK,EAALA;IAAP,CAAlB,CAArC;EAAA,CAAnC,CAHmG,CAKnG;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACA,IAAMK,gBAAgB,GAAGC,mBAAmB,CAACP,MAAM,GAAGJ,IAAH,GAAUE,QAAjB,EAA2BI,SAA3B,EAAsC;IACjFF,MAAM,EAANA,MADiF;IAEjFD,GAAG,EAAHA,GAFiF;IAGjFE,KAAK,EAALA,KAHiF;IAIjFJ,QAAQ,EAAEG,MAAM,GAAGH,QAAH,GAAcD,IAJmD,CAKjF;;EALiF,CAAtC,CAA5C;;EAQA,IAAIU,gBAAgB,KAAKE,SAAzB,EAAoC;IACnC;IACA;IACA;EACA;;EAED,IAAIC,gBAAJ;;EAEA,IAAIb,IAAJ,EAAU;IACT,IAAIA,IAAI,CAACF,mBAAT,EAA8B;MAC7Be,gBAAgB,GAAGb,IAAI,CAACF,mBAAL,CAAyBQ,SAAzB,EAAoC;QACtDE,0BAA0B,EAA1BA,0BADsD;QAEtDb,gBAAgB,EAAhBA,gBAFsD;QAGtDQ,GAAG,EAAHA,GAHsD;QAItDC,MAAM,EAANA,MAJsD;QAKtDC,KAAK,EAALA;MALsD,CAApC,CAAnB;IAOA;;IAED,IAAIQ,gBAAgB,KAAKD,SAAzB,EAAoC;MACnC;MACA,IAAMH,IAAI,GAAGT,IAAI,CAACS,IAAL,IAAaT,IAAI,CAACc,QAA/B;;MACA,IAAIL,IAAJ,EAAU;QACT;QACA;QACA;QACA;QACAI,gBAAgB,GAAGL,0BAA0B,CAACC,IAAD,CAA7C;MACA;IACD;EACD;;EAED,IAAII,gBAAgB,KAAKD,SAAzB,EAAoC;IACnC,OAAOF,gBAAP;EACA;;EAED,OAAOK,IAAI,CAACC,GAAL,CAASH,gBAAT,EAA2BH,gBAA3B,CAAP;AACA;AAED,OAAO,SAASO,gBAAT,CAA0BC,iBAA1B,EAA6CZ,SAA7C,SAA0F;EAAA,IAAhCH,GAAgC,SAAhCA,GAAgC;EAAA,IAA3BC,MAA2B,SAA3BA,MAA2B;EAAA,IAAnBC,KAAmB,SAAnBA,KAAmB;EAAA,IAAZJ,QAAY,SAAZA,QAAY;EAChG;EACA;EACA;EACA;EACA;EACA,IAAMkB,OAAO,GAAGzB,cAAc,CAACwB,iBAAD,EAAoB;IAAEZ,SAAS,EAATA,SAAF;IAAaH,GAAG,EAAHA,GAAb;IAAkBC,MAAM,EAANA,MAAlB;IAA0BC,KAAK,EAALA,KAA1B;IAAiCJ,QAAQ,EAARA;EAAjC,CAApB,CAA9B;;EACA,IAAIkB,OAAO,KAAKP,SAAhB,EAA2B;IAC1B;EACA;;EACD,IAAIR,MAAJ,EAAY;IACX;IACA;IACA;IACA;IACA;IACA,OAAOE,SAAS,GAAGa,OAAO,GAAG,IAAtB,GAA6B,CAApC;EACA,CAPD,MAOO;IACN;IACA;IACA;IACA;IACA;IAEA;IACA,IAAIA,OAAO,KAAK,CAAZ,IAAiBb,SAAS,KAAKH,GAAnC,EAAwC;MACvC,OAAON,QAAP;IACA;;IAED,OAAOS,SAAS,GAAGa,OAAO,GAAG,IAA7B;EACA;AACD;AAED,OAAO,SAASR,mBAAT,CAA6BX,IAA7B,EAAmCM,SAAnC,SAKJ;EAAA,IAJFH,GAIE,SAJFA,GAIE;EAAA,IAHFC,MAGE,SAHFA,MAGE;EAAA,IAFFC,KAEE,SAFFA,KAEE;EAAA,IADFJ,QACE,SADFA,QACE;;EACF,IAAID,IAAJ,EAAU;IACT,IAAMoB,aAAa,GAAGH,gBAAgB,CAACjB,IAAD,EAAOM,SAAP,EAAkB;MACvDH,GAAG,EAAHA,GADuD;MAEvDC,MAAM,EAANA,MAFuD;MAGvDC,KAAK,EAALA,KAHuD;MAIvDJ,QAAQ,EAARA;IAJuD,CAAlB,CAAtC;;IAMA,IAAImB,aAAa,KAAKR,SAAtB,EAAiC;MAChC;IACA;;IACD,OAAOQ,aAAa,GAAGjB,GAAvB;EACA,CAXD,MAWO;IACN,IAAIC,MAAJ,EAAY;MACX;MACA;MACA,OAAOE,SAAS,GAAGH,GAAZ,GAAkB,CAAzB;IACA,CAJD,MAIO;MACN;MACA,OAAON,QAAP;IACA;EACD;AACD"}