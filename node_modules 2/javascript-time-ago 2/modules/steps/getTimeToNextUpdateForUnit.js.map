{"version": 3, "file": "getTimeToNextUpdateForUnit.js", "names": ["getSecondsInUnit", "getRoundFunction", "getDiffRatioToNextRoundedNumber", "getTimeToNextUpdateForUnit", "unit", "timestamp", "now", "round", "unitDenominator", "future", "preciseAmount", "Math", "abs", "roundedAmount", "getDiffToPreviousRoundedNumber", "getDiffToNextRoundedNumber"], "sources": ["../../source/steps/getTimeToNextUpdateForUnit.js"], "sourcesContent": ["import { getSecondsInUnit } from './units.js'\r\nimport { getRoundFunction, getDiffRatioToNextRoundedNumber } from '../round.js'\r\n\r\n/**\r\n * Gets the time to next update for a step with a time unit defined.\r\n * @param  {string} unit\r\n * @param  {number} date — The date passed to `.format()`, converted to a timestamp.\r\n * @param  {number} options.now\r\n * @param  {string} [options.round] — (undocumented) Rounding mechanism.\r\n * @return {number} [timeToNextUpdate]\r\n */\r\nexport default function getTimeToNextUpdateForUnit(unit, timestamp, { now, round }) {\r\n\t// For some units, like \"now\", there's no defined amount of seconds in them.\r\n\tif (!getSecondsInUnit(unit)) {\r\n\t\t// If there's no amount of seconds defined for this unit\r\n\t\t// then the update interval can't be determined reliably.\r\n\t\treturn\r\n\t}\r\n\tconst unitDenominator = getSecondsInUnit(unit) * 1000\r\n\tconst future = timestamp > now\r\n\tconst preciseAmount = Math.abs(timestamp - now)\r\n\tconst roundedAmount = getRoundFunction(round)(preciseAmount / unitDenominator) * unitDenominator\r\n\tif (future) {\r\n\t\tif (roundedAmount > 0) {\r\n\t\t\t// Amount decreases with time.\r\n\t\t\treturn (preciseAmount - roundedAmount) +\r\n\t\t\t\tgetDiffToPreviousRoundedNumber(round, unitDenominator)\r\n\t\t} else {\r\n\t\t\t// Refresh right after the zero point,\r\n\t\t\t// when \"future\" changes to \"past\".\r\n\t\t\treturn (preciseAmount - roundedAmount) + 1\r\n\t\t}\r\n\t}\r\n \t// Amount increases with time.\r\n\treturn -(preciseAmount - roundedAmount) + getDiffToNextRoundedNumber(round, unitDenominator)\r\n}\r\n\r\nfunction getDiffToNextRoundedNumber(round, unitDenominator) {\r\n\treturn getDiffRatioToNextRoundedNumber(round) * unitDenominator\r\n}\r\n\r\nfunction getDiffToPreviousRoundedNumber(round, unitDenominator) {\r\n\treturn (1 - getDiffRatioToNextRoundedNumber(round)) * unitDenominator + 1\r\n}"], "mappings": "AAAA,SAASA,gBAAT,QAAiC,YAAjC;AACA,SAASC,gBAAT,EAA2BC,+BAA3B,QAAkE,aAAlE;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,eAAe,SAASC,0BAAT,CAAoCC,IAApC,EAA0CC,SAA1C,QAAqE;EAAA,IAAdC,GAAc,QAAdA,GAAc;EAAA,IAATC,KAAS,QAATA,KAAS;;EACnF;EACA,IAAI,CAACP,gBAAgB,CAACI,IAAD,CAArB,EAA6B;IAC5B;IACA;IACA;EACA;;EACD,IAAMI,eAAe,GAAGR,gBAAgB,CAACI,IAAD,CAAhB,GAAyB,IAAjD;EACA,IAAMK,MAAM,GAAGJ,SAAS,GAAGC,GAA3B;EACA,IAAMI,aAAa,GAAGC,IAAI,CAACC,GAAL,CAASP,SAAS,GAAGC,GAArB,CAAtB;EACA,IAAMO,aAAa,GAAGZ,gBAAgB,CAACM,KAAD,CAAhB,CAAwBG,aAAa,GAAGF,eAAxC,IAA2DA,eAAjF;;EACA,IAAIC,MAAJ,EAAY;IACX,IAAII,aAAa,GAAG,CAApB,EAAuB;MACtB;MACA,OAAQH,aAAa,GAAGG,aAAjB,GACNC,8BAA8B,CAACP,KAAD,EAAQC,eAAR,CAD/B;IAEA,CAJD,MAIO;MACN;MACA;MACA,OAAQE,aAAa,GAAGG,aAAjB,GAAkC,CAAzC;IACA;EACD,CArBkF,CAsBlF;;;EACD,OAAO,EAAEH,aAAa,GAAGG,aAAlB,IAAmCE,0BAA0B,CAACR,KAAD,EAAQC,eAAR,CAApE;AACA;;AAED,SAASO,0BAAT,CAAoCR,KAApC,EAA2CC,eAA3C,EAA4D;EAC3D,OAAON,+BAA+B,CAACK,KAAD,CAA/B,GAAyCC,eAAhD;AACA;;AAED,SAASM,8BAAT,CAAwCP,KAAxC,EAA+CC,eAA/C,EAAgE;EAC/D,OAAO,CAAC,IAAIN,+BAA+B,CAACK,KAAD,CAApC,IAA+CC,eAA/C,GAAiE,CAAxE;AACA"}