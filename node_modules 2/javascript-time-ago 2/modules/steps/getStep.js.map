{"version": 3, "file": "getStep.js", "names": ["getStepDenominator", "getStepMinTime", "getRoundFunction", "getStep", "steps", "secondsPassed", "now", "future", "round", "units", "getNextStep", "filterStepsByUnits", "step", "_getStep", "prevStep", "indexOf", "nextStep", "undefined", "length", "i", "getStepIndex", "granularity", "secondsPassedGranular", "Math", "abs", "options", "minTime", "timestamp", "filter", "unit", "formatAs"], "sources": ["../../source/steps/getStep.js"], "sourcesContent": ["import getStepDenominator from './getStepDenominator.js'\r\nimport getStepMinTime from './getStepMinTime.js'\r\nimport { getRoundFunction } from '../round.js'\r\n\r\n/**\r\n * Finds an appropriate `step` of `steps` for the time interval (in seconds).\r\n *\r\n * @param {Object[]} steps - Time formatting steps.\r\n *\r\n * @param {number} secondsPassed - Time interval (in seconds).\r\n *                                 `< 0` for past dates and `> 0` for future dates.\r\n *\r\n * @param {number} options.now - Current timestamp.\r\n *\r\n * @param {boolean} [options.future] - Whether the date should be formatted as a future one\r\n *                                     instead of a past one.\r\n *\r\n * @param {string} [options.round] - (undocumented) Rounding mechanism.\r\n *\r\n * @param {string[]} [options.units] - A list of allowed time units.\r\n *                                     (Example: ['second', 'minute', 'hour', …])\r\n *\r\n * @param {boolean} [options.getNextStep] - Pass true to return `[step, nextStep]` instead of just `step`.\r\n *\r\n * @return {Object|Object[]} [step] — Either a `step` or `[prevStep, step, nextStep]`.\r\n */\r\nexport default function getStep(steps, secondsPassed, { now, future, round, units, getNextStep }) {\r\n\t// Ignore steps having not-supported time units in `formatAs`.\r\n\tsteps = filterStepsByUnits(steps, units)\r\n\tconst step = _getStep(steps, secondsPassed, { now, future, round })\r\n\tif (getNextStep) {\r\n\t\tif (step) {\r\n\t\t\tconst prevStep = steps[steps.indexOf(step) - 1]\r\n\t\t\tconst nextStep = steps[steps.indexOf(step) + 1]\r\n\t\t\treturn [prevStep, step, nextStep]\r\n\t\t}\r\n\t\treturn [undefined, undefined, steps[0]]\r\n\t}\r\n\treturn step\r\n}\r\n\r\nfunction _getStep(steps, secondsPassed, { now, future, round }) {\r\n\t// If no steps fit the conditions then return nothing.\r\n\tif (steps.length === 0) {\r\n\t\treturn\r\n\t}\r\n\r\n\t// Find the most appropriate step.\r\n\tconst i = getStepIndex(steps, secondsPassed, {\r\n\t\tnow,\r\n\t\tfuture: future || secondsPassed < 0,\r\n\t\tround\r\n\t})\r\n\r\n\t// If no step is applicable the return nothing.\r\n\tif (i === -1) {\r\n\t\treturn\r\n\t}\r\n\r\n\tconst step = steps[i]\r\n\r\n\t// Apply granularity to the time amount\r\n\t// (and fall back to the previous step\r\n\t//  if the first level of granularity\r\n\t//  isn't met by this amount)\r\n\tif (step.granularity) {\r\n\t\t// Recalculate the amount of seconds passed based on `granularity`.\r\n\t\tconst secondsPassedGranular = getRoundFunction(round)((Math.abs(secondsPassed) / getStepDenominator(step)) / step.granularity) * step.granularity\r\n\t\t// If the granularity for this step is too high,\r\n\t\t// then fall back to the previous step.\r\n\t\t// (if there is any previous step)\r\n\t\tif (secondsPassedGranular === 0 && i > 0) {\r\n\t\t\treturn steps[i - 1]\r\n\t\t}\r\n\t}\r\n\r\n\treturn step\r\n}\r\n\r\n/**\r\n * Iterates through steps until it finds the maximum one satisfying the `minTime` threshold.\r\n * @param  {Object} steps - Steps.\r\n * @param  {number} secondsPassed - How much seconds have passed since the date till `now`.\r\n * @param  {number} options.now - Current timestamp.\r\n * @param  {boolean} options.future - Whether the time interval should be formatted as a future one.\r\n * @param  {number} [i] - Gradation step currently being tested.\r\n * @return {number} Gradation step index.\r\n */\r\nfunction getStepIndex(steps, secondsPassed, options, i = 0) {\r\n\tconst minTime = getStepMinTime(steps[i], {\r\n\t\tprevStep: steps[i - 1],\r\n\t\ttimestamp: options.now - secondsPassed * 1000,\r\n\t\t...options\r\n\t})\r\n\t// If `minTime` isn't defined or deduceable for this step, then stop.\r\n\tif (minTime === undefined) {\r\n\t\treturn i - 1\r\n\t}\r\n\t// If the `minTime` threshold for moving from previous step\r\n\t// to this step is too high then return the previous step.\r\n\tif (Math.abs(secondsPassed) < minTime) {\r\n\t\treturn i - 1\r\n\t}\r\n\t// If it's the last step then return it.\r\n\tif (i === steps.length - 1) {\r\n\t\treturn i\r\n\t}\r\n\t// Move to the next step.\r\n\treturn getStepIndex(steps, secondsPassed, options, i + 1)\r\n}\r\n\r\n/**\r\n * Leaves only allowed steps.\r\n * @param  {Object[]} steps\r\n * @param  {string[]} units - Allowed time units.\r\n * @return {Object[]}\r\n */\r\nfunction filterStepsByUnits(steps, units) {\r\n\treturn steps.filter(({ unit, formatAs }) => {\r\n\t\t// \"unit\" is now called \"formatAs\".\r\n\t\tunit = unit || formatAs\r\n\t\t// If this step has a `unit` defined\r\n\t\t// then this `unit` must be in the list of allowed `units`.\r\n\t\tif (unit) {\r\n\t\t\treturn units.indexOf(unit) >= 0\r\n\t\t}\r\n\t\t// A step is not required to specify a `unit`:\r\n\t\t// alternatively, it could specify `format()`.\r\n\t\t// (see \"twitter\" style for an example)\r\n\t\treturn true\r\n\t})\r\n}"], "mappings": ";;;;;;AAAA,OAAOA,kBAAP,MAA+B,yBAA/B;AACA,OAAOC,cAAP,MAA2B,qBAA3B;AACA,SAASC,gBAAT,QAAiC,aAAjC;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,eAAe,SAASC,OAAT,CAAiBC,KAAjB,EAAwBC,aAAxB,QAAmF;EAAA,IAA1CC,GAA0C,QAA1CA,GAA0C;EAAA,IAArCC,MAAqC,QAArCA,MAAqC;EAAA,IAA7BC,KAA6B,QAA7BA,KAA6B;EAAA,IAAtBC,KAAsB,QAAtBA,KAAsB;EAAA,IAAfC,WAAe,QAAfA,WAAe;EACjG;EACAN,KAAK,GAAGO,kBAAkB,CAACP,KAAD,EAAQK,KAAR,CAA1B;;EACA,IAAMG,IAAI,GAAGC,QAAQ,CAACT,KAAD,EAAQC,aAAR,EAAuB;IAAEC,GAAG,EAAHA,GAAF;IAAOC,MAAM,EAANA,MAAP;IAAeC,KAAK,EAALA;EAAf,CAAvB,CAArB;;EACA,IAAIE,WAAJ,EAAiB;IAChB,IAAIE,IAAJ,EAAU;MACT,IAAME,QAAQ,GAAGV,KAAK,CAACA,KAAK,CAACW,OAAN,CAAcH,IAAd,IAAsB,CAAvB,CAAtB;MACA,IAAMI,QAAQ,GAAGZ,KAAK,CAACA,KAAK,CAACW,OAAN,CAAcH,IAAd,IAAsB,CAAvB,CAAtB;MACA,OAAO,CAACE,QAAD,EAAWF,IAAX,EAAiBI,QAAjB,CAAP;IACA;;IACD,OAAO,CAACC,SAAD,EAAYA,SAAZ,EAAuBb,KAAK,CAAC,CAAD,CAA5B,CAAP;EACA;;EACD,OAAOQ,IAAP;AACA;;AAED,SAASC,QAAT,CAAkBT,KAAlB,EAAyBC,aAAzB,SAAgE;EAAA,IAAtBC,GAAsB,SAAtBA,GAAsB;EAAA,IAAjBC,MAAiB,SAAjBA,MAAiB;EAAA,IAATC,KAAS,SAATA,KAAS;;EAC/D;EACA,IAAIJ,KAAK,CAACc,MAAN,KAAiB,CAArB,EAAwB;IACvB;EACA,CAJ8D,CAM/D;;;EACA,IAAMC,CAAC,GAAGC,YAAY,CAAChB,KAAD,EAAQC,aAAR,EAAuB;IAC5CC,GAAG,EAAHA,GAD4C;IAE5CC,MAAM,EAAEA,MAAM,IAAIF,aAAa,GAAG,CAFU;IAG5CG,KAAK,EAALA;EAH4C,CAAvB,CAAtB,CAP+D,CAa/D;;EACA,IAAIW,CAAC,KAAK,CAAC,CAAX,EAAc;IACb;EACA;;EAED,IAAMP,IAAI,GAAGR,KAAK,CAACe,CAAD,CAAlB,CAlB+D,CAoB/D;EACA;EACA;EACA;;EACA,IAAIP,IAAI,CAACS,WAAT,EAAsB;IACrB;IACA,IAAMC,qBAAqB,GAAGpB,gBAAgB,CAACM,KAAD,CAAhB,CAAyBe,IAAI,CAACC,GAAL,CAASnB,aAAT,IAA0BL,kBAAkB,CAACY,IAAD,CAA7C,GAAuDA,IAAI,CAACS,WAApF,IAAmGT,IAAI,CAACS,WAAtI,CAFqB,CAGrB;IACA;IACA;;IACA,IAAIC,qBAAqB,KAAK,CAA1B,IAA+BH,CAAC,GAAG,CAAvC,EAA0C;MACzC,OAAOf,KAAK,CAACe,CAAC,GAAG,CAAL,CAAZ;IACA;EACD;;EAED,OAAOP,IAAP;AACA;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,SAASQ,YAAT,CAAsBhB,KAAtB,EAA6BC,aAA7B,EAA4CoB,OAA5C,EAA4D;EAAA,IAAPN,CAAO,uEAAH,CAAG;EAC3D,IAAMO,OAAO,GAAGzB,cAAc,CAACG,KAAK,CAACe,CAAD,CAAN;IAC7BL,QAAQ,EAAEV,KAAK,CAACe,CAAC,GAAG,CAAL,CADc;IAE7BQ,SAAS,EAAEF,OAAO,CAACnB,GAAR,GAAcD,aAAa,GAAG;EAFZ,GAG1BoB,OAH0B,EAA9B,CAD2D,CAM3D;;EACA,IAAIC,OAAO,KAAKT,SAAhB,EAA2B;IAC1B,OAAOE,CAAC,GAAG,CAAX;EACA,CAT0D,CAU3D;EACA;;;EACA,IAAII,IAAI,CAACC,GAAL,CAASnB,aAAT,IAA0BqB,OAA9B,EAAuC;IACtC,OAAOP,CAAC,GAAG,CAAX;EACA,CAd0D,CAe3D;;;EACA,IAAIA,CAAC,KAAKf,KAAK,CAACc,MAAN,GAAe,CAAzB,EAA4B;IAC3B,OAAOC,CAAP;EACA,CAlB0D,CAmB3D;;;EACA,OAAOC,YAAY,CAAChB,KAAD,EAAQC,aAAR,EAAuBoB,OAAvB,EAAgCN,CAAC,GAAG,CAApC,CAAnB;AACA;AAED;AACA;AACA;AACA;AACA;AACA;;;AACA,SAASR,kBAAT,CAA4BP,KAA5B,EAAmCK,KAAnC,EAA0C;EACzC,OAAOL,KAAK,CAACwB,MAAN,CAAa,iBAAwB;IAAA,IAArBC,IAAqB,SAArBA,IAAqB;IAAA,IAAfC,QAAe,SAAfA,QAAe;IAC3C;IACAD,IAAI,GAAGA,IAAI,IAAIC,QAAf,CAF2C,CAG3C;IACA;;IACA,IAAID,IAAJ,EAAU;MACT,OAAOpB,KAAK,CAACM,OAAN,CAAcc,IAAd,KAAuB,CAA9B;IACA,CAP0C,CAQ3C;IACA;IACA;;;IACA,OAAO,IAAP;EACA,CAZM,CAAP;AAaA"}