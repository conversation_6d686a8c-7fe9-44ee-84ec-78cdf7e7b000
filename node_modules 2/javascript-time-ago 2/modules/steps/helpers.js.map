{"version": 3, "file": "helpers.js", "names": ["getDate", "value", "Date"], "sources": ["../../source/steps/helpers.js"], "sourcesContent": ["// Looks like this one's deprecated.\r\n// /**\r\n//  * Returns a step corresponding to the unit.\r\n//  * @param  {Object[]} steps\r\n//  * @param  {string} unit\r\n//  * @return {?Object}\r\n//  */\r\n// export function getStepForUnit(steps, unit) {\r\n// \tfor (const step of steps) {\r\n// \t\tif (step.unit === unit) {\r\n// \t\t\treturn step\r\n// \t\t}\r\n// \t}\r\n// }\r\n\r\n// Looks like this one won't be used in the next major version.\r\n/**\r\n * Converts value to a `Date`\r\n * @param {(number|Date)} value\r\n * @return {Date}\r\n */\r\nexport function getDate(value) {\r\n\treturn value instanceof Date ? value : new Date(value)\r\n}"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASA,OAAT,CAAiBC,KAAjB,EAAwB;EAC9B,OAAOA,KAAK,YAAYC,IAAjB,GAAwBD,KAAxB,GAAgC,IAAIC,IAAJ,CAASD,KAAT,CAAvC;AACA"}