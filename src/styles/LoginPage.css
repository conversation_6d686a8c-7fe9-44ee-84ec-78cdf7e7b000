/* 🌊 Liquid Glass Login Interface - 液态玻璃登录界面设计系统 */
@import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Rajdhani:wght@300;400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

/* ============== 液态玻璃设计系统变量 ============== */
:root {
  /* 主色调系统 - 青蓝色调 */
  --liquid-primary: #00d4ff;
  --liquid-primary-dark: #0099cc;
  --liquid-secondary: #4dc8ff;
  --liquid-accent: #80e5ff;
  --liquid-glow: rgba(0, 212, 255, 0.6);

  /* 深度背景系统 */
  --depth-bg-1: #0a0a0f;
  --depth-bg-2: #0f0f1a;
  --depth-bg-3: #1a1a2e;
  --depth-bg-4: #16213e;
  --depth-bg-5: #0d1421;

  /* 液态玻璃透明度系统 */
  --glass-transparency: rgba(255, 255, 255, 0.03);
  --glass-border: rgba(0, 212, 255, 0.15);
  --glass-highlight: rgba(255, 255, 255, 0.08);
  --glass-shadow: rgba(0, 0, 0, 0.4);
  --glass-backdrop: blur(25px) saturate(180%);

  /* 文字颜色系统 */
  --text-primary: rgba(255, 255, 255, 0.95);
  --text-secondary: rgba(255, 255, 255, 0.75);
  --text-muted: rgba(255, 255, 255, 0.45);
  --text-accent: var(--liquid-primary);

  /* 状态颜色 */
  --success-color: #10b981;
  --warning-color: #f59e0b;
  --error-color: #ef4444;

  /* 阴影系统 */
  --liquid-shadow-1: 0 8px 32px rgba(0, 212, 255, 0.15);
  --liquid-shadow-2: 0 16px 64px rgba(0, 212, 255, 0.1);
  --liquid-shadow-3: 0 24px 96px rgba(0, 0, 0, 0.3);

  /* 动画时间 */
  --liquid-duration-fast: 0.2s;
  --liquid-duration-normal: 0.4s;
  --liquid-duration-slow: 0.8s;

  /* 边框半径 */
  --radius-small: 8px;
  --radius-medium: 16px;
  --radius-large: 24px;
  --radius-xl: 32px;
}

/* ============== 主页面容器 - 液态玻璃基础 ============== */
.login-page {
  min-height: 100vh;
  width: 100%;
  position: relative;
  overflow: hidden;
  background: radial-gradient(ellipse at center, var(--depth-bg-4) 0%, var(--depth-bg-2) 40%, var(--depth-bg-1) 100%);
  display: flex;
  flex-direction: column;
  font-family: 'Inter', 'Rajdhani', sans-serif;
  color: var(--text-primary);
  /* GPU加速优化 */
  transform: translateZ(0);
  will-change: transform;
}

/* 点阵矩阵背景系统 */
.login-page::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    radial-gradient(circle at 1px 1px, rgba(0, 212, 255, 0.15) 1px, transparent 0),
    radial-gradient(circle at 1px 1px, rgba(0, 212, 255, 0.08) 1px, transparent 0);
  background-size: 40px 40px, 80px 80px;
  background-position: 0 0, 20px 20px;
  opacity: 0.4;
  z-index: 1;
  animation: dotMatrixFloat 60s linear infinite;
  pointer-events: none;
}

/* 点阵矩阵动画 */
@keyframes dotMatrixFloat {
  0% {
    transform: translate(0, 0);
  }
  25% {
    transform: translate(-20px, -10px);
  }
  50% {
    transform: translate(-40px, -20px);
  }
  75% {
    transform: translate(-20px, -30px);
  }
  100% {
    transform: translate(0, -40px);
  }
}

/* 增强的网格叠加层 */
.login-page::after {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    linear-gradient(rgba(0, 212, 255, 0.03) 1px, transparent 1px),
    linear-gradient(90deg, rgba(0, 212, 255, 0.03) 1px, transparent 1px);
  background-size: 120px 120px;
  opacity: 0.6;
  z-index: 2;
  animation: gridPulse 8s ease-in-out infinite;
  pointer-events: none;
}

@keyframes gridPulse {
  0%, 100% { opacity: 0.4; }
  50% { opacity: 0.7; }
}

/* 3D背景容器 - 简化性能 */
.login-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 3;
  opacity: 0.7;
  /* GPU加速 */
  transform: translateZ(0);
  will-change: transform;
}

/* ============== 浮动导航按钮 - 液态玻璃设计 ============== */
.floating-nav-buttons {
  position: fixed;
  top: 24px;
  left: 24px;
  right: 24px;
  z-index: 1000;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  pointer-events: none;
}

.floating-back-button,
.floating-language-toggle {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 12px 20px;
  background: var(--glass-transparency);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-medium);
  color: var(--text-primary);
  text-decoration: none;
  font-family: 'Inter', sans-serif;
  font-weight: 500;
  font-size: 14px;
  cursor: pointer;
  transition: all var(--liquid-duration-normal) cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: var(--glass-backdrop);
  pointer-events: auto;
  position: relative;
  overflow: hidden;
  /* 液态玻璃阴影 */
  box-shadow:
    var(--liquid-shadow-1),
    inset 0 1px 0 var(--glass-highlight);
}

/* 液态玻璃按钮悬停效果 */
.floating-back-button:hover,
.floating-language-toggle:hover {
  background: rgba(0, 212, 255, 0.08);
  border-color: var(--liquid-primary);
  transform: translateY(-2px);
  box-shadow:
    var(--liquid-shadow-2),
    0 0 20px var(--liquid-glow),
    inset 0 1px 0 rgba(255, 255, 255, 0.15);
}

/* 液态玻璃按钮内部光效 */
.floating-back-button::before,
.floating-language-toggle::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, var(--glass-highlight), transparent);
  transition: left var(--liquid-duration-slow);
}

.floating-back-button:hover::before,
.floating-language-toggle:hover::before {
  left: 100%;
}

.back-icon,
.language-icon {
  font-size: 18px;
  opacity: 0.9;
}

/* 语言下拉菜单 */
.floating-language-dropdown {
  position: relative;
  pointer-events: auto;
}

.dropdown-arrow {
  font-size: 12px;
  transition: transform var(--transition-normal);
  margin-left: 8px;
}

.dropdown-arrow.open {
  transform: rotate(180deg);
}

.language-dropdown-menu {
  position: fixed;
  top: calc(100% + 8px);
  right: 0;
  min-width: 160px;
  background: var(--surface-color);
  border: 1px solid var(--surface-border);
  border-radius: var(--border-radius);
  backdrop-filter: blur(25px);
  box-shadow:
    0 12px 40px rgba(0, 0, 0, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  overflow: visible;
  animation: dropdownFadeIn 0.2s ease-out;
  z-index: 1150;
  /* 防止被截断 */
  will-change: transform, opacity;
}

@keyframes dropdownFadeIn {
  from {
    opacity: 0;
    transform: translateY(-8px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.language-option {
  display: flex;
  align-items: center;
  gap: 12px;
  width: 100%;
  padding: 12px 16px;
  background: none;
  border: none;
  color: var(--text-primary);
  font-family: 'Inter', sans-serif;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all var(--transition-normal);
  text-align: left;
}

.language-option:hover {
  background: rgba(77, 200, 255, 0.1);
  color: var(--primary-color);
}

.language-option.active {
  background: rgba(77, 200, 255, 0.15);
  color: var(--primary-color);
}

.flag-icon {
  font-size: 16px;
}

/* ============== 主要内容容器 - 液态玻璃布局 ============== */
.login-container {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 80px 20px 20px;
  position: relative;
  z-index: 10;
  min-height: 100vh;
  /* 添加微妙的入场动画 */
  animation: liquidFadeIn 1.2s ease-out;
}

@keyframes liquidFadeIn {
  from {
    opacity: 0;
    transform: translateY(30px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.login-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 80px;
  max-width: 1400px;
  width: 100%;
  align-items: stretch;
  min-height: 600px;
  position: relative;
  /* 防止内容溢出 */
  contain: layout style;
}

/* ============== 左侧品牌区域 - 液态玻璃美学 ============== */
.brand-section {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
  padding: 40px;
  position: relative;
  /* 添加微妙的入场动画延迟 */
  animation: liquidSlideIn 1s ease-out 0.3s both;
}

@keyframes liquidSlideIn {
  from {
    opacity: 0;
    transform: translateX(-40px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.brand-content {
  max-width: 500px;
  position: relative;
}

.brand-title {
  margin: 0 0 24px 0;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.welcome-text {
  font-family: 'Rajdhani', sans-serif;
  font-size: 26px;
  font-weight: 400;
  color: var(--text-secondary);
  letter-spacing: 1.5px;
  text-transform: uppercase;
  opacity: 0.9;
}

.app-name {
  font-family: 'Orbitron', monospace;
  font-size: 52px;
  font-weight: 700;
  background: linear-gradient(135deg, var(--liquid-primary), var(--liquid-accent), var(--liquid-secondary));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  letter-spacing: 3px;
  text-shadow: 0 0 40px var(--liquid-glow);
  position: relative;
  /* 添加发光效果 */
  filter: drop-shadow(0 0 20px rgba(0, 212, 255, 0.3));
}

.brand-subtitle {
  font-family: 'Rajdhani', sans-serif;
  font-size: 20px;
  font-weight: 400;
  color: var(--text-muted);
  margin: 0 0 48px 0;
  letter-spacing: 0.8px;
  line-height: 1.6;
  opacity: 0.8;
}

/* 装饰性元素 - 液态科技线条 */
.brand-decoration {
  margin-top: 48px;
  position: relative;
}

.tech-lines {
  display: flex;
  flex-direction: column;
  gap: 12px;
  position: relative;
}

.tech-line {
  height: 3px;
  background: linear-gradient(90deg, var(--liquid-primary), var(--liquid-accent), transparent);
  border-radius: 2px;
  animation: liquidTechPulse 4s ease-in-out infinite;
  position: relative;
  overflow: hidden;
  box-shadow: 0 0 10px rgba(0, 212, 255, 0.3);
}

.tech-line::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  animation: liquidFlow 3s ease-in-out infinite;
}

.tech-line:nth-child(1) {
  width: 140px;
  animation-delay: 0s;
}

.tech-line:nth-child(1)::before {
  animation-delay: 0.5s;
}

.tech-line:nth-child(2) {
  width: 100px;
  animation-delay: 0.8s;
}

.tech-line:nth-child(2)::before {
  animation-delay: 1.3s;
}

.tech-line:nth-child(3) {
  width: 120px;
  animation-delay: 1.6s;
}

.tech-line:nth-child(3)::before {
  animation-delay: 2.1s;
}

@keyframes liquidTechPulse {
  0%, 100% {
    opacity: 0.5;
    transform: scaleX(1);
  }
  50% {
    opacity: 1;
    transform: scaleX(1.05);
  }
}

@keyframes liquidFlow {
  0% {
    left: -100%;
    opacity: 0;
  }
  50% {
    opacity: 1;
  }
  100% {
    left: 100%;
    opacity: 0;
  }
}

/* ============== 右侧表单区域 - 液态玻璃容器 ============== */
.form-section {
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  /* 添加入场动画延迟 */
  animation: liquidSlideIn 1s ease-out 0.6s both;
}

.form-container {
  background: var(--glass-transparency);
  backdrop-filter: var(--glass-backdrop);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-xl);
  padding: 40px;
  width: 100%;
  max-width: 520px;
  max-height: 90vh;
  overflow-y: auto;
  position: relative;
  transition: all var(--liquid-duration-normal) cubic-bezier(0.4, 0, 0.2, 1);
  /* 液态玻璃阴影系统 */
  box-shadow:
    var(--liquid-shadow-3),
    0 0 0 1px rgba(255, 255, 255, 0.05),
    inset 0 1px 0 var(--glass-highlight),
    inset 0 -1px 0 rgba(0, 0, 0, 0.1);
  /* GPU优化 */
  transform: translateZ(0);
  will-change: transform;
  contain: layout style;
}

/* 液态玻璃容器内部光效 */
.form-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg,
    rgba(0, 212, 255, 0.05) 0%,
    transparent 30%,
    transparent 70%,
    rgba(0, 212, 255, 0.03) 100%
  );
  border-radius: var(--radius-xl);
  opacity: 0.8;
  z-index: 0;
  pointer-events: none;
}

/* 液态玻璃容器悬停效果 */
.form-container:hover {
  border-color: rgba(0, 212, 255, 0.3);
  box-shadow:
    var(--liquid-shadow-3),
    0 0 40px rgba(0, 212, 255, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.12);
  transform: translateY(-2px);
}

/* 液态玻璃容器外部光晕 */
.form-container::after {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(45deg,
    rgba(0, 212, 255, 0.1) 0%,
    rgba(0, 212, 255, 0.05) 25%,
    rgba(0, 212, 255, 0.1) 50%,
    rgba(0, 212, 255, 0.05) 75%,
    rgba(0, 212, 255, 0.1) 100%
  );
  border-radius: calc(var(--radius-xl) + 2px);
  z-index: -1;
  opacity: 0;
  transition: opacity var(--liquid-duration-normal);
  filter: blur(1px);
}

.form-container:hover::after {
  opacity: 0.6;
}

/* ============== 表单头部 - 液态玻璃标题 ============== */
.form-header {
  text-align: center;
  margin-bottom: 32px;
  position: relative;
  z-index: 1;
}

.form-title {
  font-family: 'Orbitron', monospace;
  font-size: 28px;
  font-weight: 600;
  margin: 0 0 12px 0;
  letter-spacing: 1.5px;
  background: linear-gradient(135deg, var(--text-primary), var(--liquid-primary), var(--liquid-accent));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  position: relative;
  /* 添加发光效果 */
  filter: drop-shadow(0 0 15px rgba(0, 212, 255, 0.2));
}

.form-subtitle {
  font-family: 'Rajdhani', sans-serif;
  font-size: 16px;
  color: var(--text-muted);
  margin: 0;
  letter-spacing: 0.8px;
  opacity: 0.9;
}

/* ============== 表单样式 - 液态玻璃输入字段 ============== */
.login-form {
  position: relative;
  z-index: 1;
}

.form-group {
  margin-bottom: 28px;
  position: relative;
}

.form-group label {
  display: block;
  font-family: 'Inter', sans-serif;
  font-size: 12px;
  font-weight: 600;
  color: var(--text-secondary);
  margin-bottom: 10px;
  letter-spacing: 1px;
  text-transform: uppercase;
  opacity: 0.9;
}

/* 输入字段包装器 */
.input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.form-group input {
  width: 100%;
  padding: 18px 24px;
  padding-right: 55px; /* 为图标留出空间 */
  background: var(--glass-transparency);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-medium);
  color: var(--text-primary);
  font-family: 'Inter', sans-serif;
  font-size: 15px;
  font-weight: 400;
  transition: all var(--liquid-duration-normal) cubic-bezier(0.4, 0, 0.2, 1);
  box-sizing: border-box;
  /* 液态玻璃效果 */
  backdrop-filter: blur(15px) saturate(150%);
  box-shadow:
    0 4px 20px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 var(--glass-highlight);
  position: relative;
  overflow: hidden;
}

/* 液态玻璃输入字段内部光效 */
.form-group input::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, var(--glass-highlight), transparent);
  transition: left var(--liquid-duration-slow);
  pointer-events: none;
}

.form-group input:focus {
  outline: none;
  border-color: var(--liquid-primary);
  background: rgba(0, 212, 255, 0.05);
  box-shadow:
    0 0 0 3px rgba(0, 212, 255, 0.15),
    var(--liquid-shadow-1),
    inset 0 1px 0 rgba(255, 255, 255, 0.12);
  transform: translateY(-2px);
}

.form-group input:hover:not(:focus) {
  border-color: rgba(0, 212, 255, 0.3);
  background: rgba(0, 212, 255, 0.03);
  transform: translateY(-1px);
  box-shadow:
    0 6px 25px rgba(0, 0, 0, 0.15),
    inset 0 1px 0 var(--glass-highlight);
}

.form-group input::placeholder {
  color: var(--text-muted);
  transition: color var(--liquid-duration-normal);
  opacity: 0.7;
}

.form-group input:focus::placeholder {
  color: var(--text-secondary);
  opacity: 0.9;
}

/* 语言选择下拉框样式 */
.language-select {
  width: 100%;
  padding: 16px 20px;
  padding-right: 50px; /* 为图标留出空间 */
  background: rgba(255, 255, 255, 0.05);
  border: 2px solid var(--surface-border);
  border-radius: var(--border-radius);
  color: var(--text-primary);
  font-family: 'Inter', sans-serif;
  font-size: 15px;
  font-weight: 400;
  transition: all var(--transition-normal);
  box-sizing: border-box;
  cursor: pointer;
  /* 玻璃态效果 */
  backdrop-filter: blur(10px);
  box-shadow:
    0 4px 16px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.05);
  /* 移除默认箭头 */
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
}

.language-select:focus {
  outline: none;
  border-color: var(--primary-color);
  background: rgba(255, 255, 255, 0.08);
  box-shadow:
    0 0 0 4px rgba(77, 200, 255, 0.15),
    0 8px 25px rgba(77, 200, 255, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  transform: translateY(-2px);
}

.language-select:hover:not(:focus) {
  border-color: rgba(77, 200, 255, 0.4);
  background: rgba(255, 255, 255, 0.07);
  transform: translateY(-1px);
}

.language-select option {
  background: var(--background-secondary);
  color: var(--text-primary);
  padding: 12px;
  border: none;
}

/* 输入字段图标 */
.input-icon {
  position: absolute;
  right: 16px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-muted);
  pointer-events: none;
  transition: color var(--transition-normal);
}

.form-group input:focus + .input-icon {
  color: var(--primary-color);
}

/* 密码切换按钮 */
.password-toggle {
  position: absolute;
  right: 16px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: var(--text-muted);
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: all var(--transition-normal);
  display: flex;
  align-items: center;
  justify-content: center;
}

.password-toggle:hover {
  color: var(--primary-color);
  background: rgba(77, 200, 255, 0.1);
}

/* 密码强度指示器 */
.password-strength {
  margin-top: 8px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.password-strength-bar {
  flex: 1;
  height: 4px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 2px;
  overflow: hidden;
  position: relative;
}

.password-strength-bar::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  border-radius: 2px;
  transition: all var(--transition-normal);
}

.password-strength.weak .password-strength-bar::after {
  width: 33%;
  background: var(--error-color);
}

.password-strength.medium .password-strength-bar::after {
  width: 66%;
  background: var(--warning-color);
}

.password-strength.strong .password-strength-bar::after {
  width: 100%;
  background: var(--success-color);
}

.password-strength-text {
  font-family: 'Inter', sans-serif;
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.password-strength.weak .password-strength-text {
  color: var(--error-color);
}

.password-strength.medium .password-strength-text {
  color: var(--warning-color);
}

.password-strength.strong .password-strength-text {
  color: var(--success-color);
}

/* 表单选项 */
.form-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 28px;
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: 10px;
  font-family: 'Inter', sans-serif;
  font-size: 14px;
  color: var(--text-secondary);
  cursor: pointer;
  transition: color var(--transition-normal);
}

.checkbox-label:hover {
  color: var(--text-primary);
}

.checkbox-label input[type="checkbox"] {
  width: 18px;
  height: 18px;
  margin: 0;
  accent-color: var(--primary-color);
}

.forgot-password {
  font-family: 'Inter', sans-serif;
  font-size: 14px;
  color: var(--primary-color);
  background: none;
  border: none;
  cursor: pointer;
  transition: color var(--transition-normal);
  text-decoration: none;
  padding: 4px 8px;
  border-radius: 4px;
}

.forgot-password:hover {
  color: var(--secondary-color);
  background: rgba(77, 200, 255, 0.1);
}

/* ============== 液态玻璃提交按钮 ============== */
.submit-button {
  width: 100%;
  padding: 20px 28px;
  background: linear-gradient(135deg, var(--liquid-primary), var(--liquid-primary-dark));
  border: 1px solid rgba(0, 212, 255, 0.3);
  border-radius: var(--radius-medium);
  color: var(--text-primary);
  font-family: 'Inter', sans-serif;
  font-size: 16px;
  font-weight: 600;
  letter-spacing: 0.8px;
  cursor: pointer;
  transition: all var(--liquid-duration-normal) cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  margin-bottom: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  /* 液态玻璃按钮阴影 */
  box-shadow:
    var(--liquid-shadow-2),
    0 0 20px rgba(0, 212, 255, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  /* 防止白色闪烁的关键设置 */
  backdrop-filter: blur(10px);
  will-change: transform, box-shadow;
}

/* 液态玻璃按钮内部光效 */
.submit-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.15), transparent);
  transition: left var(--liquid-duration-slow);
  z-index: 1;
}

.submit-button:hover:not(:disabled)::before {
  left: 100%;
}

.submit-button:hover:not(:disabled) {
  transform: translateY(-3px) scale(1.02);
  background: linear-gradient(135deg, var(--liquid-accent), var(--liquid-primary));
  box-shadow:
    var(--liquid-shadow-3),
    0 0 40px var(--liquid-glow),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
  border-color: rgba(0, 212, 255, 0.5);
}

.submit-button:active:not(:disabled) {
  transform: translateY(-1px) scale(1.01);
  transition-duration: 0.1s;
}

.submit-button:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
  background: rgba(0, 212, 255, 0.3) !important;
  box-shadow:
    0 4px 15px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  border-color: rgba(0, 212, 255, 0.2);
}

/* 防止白色闪烁的加载状态 */
.submit-button.loading {
  background: rgba(0, 212, 255, 0.3) !important;
  border-color: rgba(0, 212, 255, 0.2) !important;
  box-shadow:
    0 4px 15px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.1) !important;
}

.submit-button.loading:hover {
  transform: none !important;
  background: rgba(0, 212, 255, 0.3) !important;
}

/* 加载状态 */
.loading-container {
  display: flex;
  align-items: center;
  gap: 10px;
}

.loading-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid var(--text-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 分割线 - 改进设计 */
.form-divider {
  display: flex;
  align-items: center;
  margin: 28px 0;
  gap: 16px;
}

.divider-line {
  flex: 1;
  height: 1px;
  background: linear-gradient(90deg, transparent, var(--surface-border), transparent);
}

.divider-text {
  font-family: 'Inter', sans-serif;
  font-size: 13px;
  font-weight: 500;
  color: var(--text-muted);
  text-transform: uppercase;
  letter-spacing: 1px;
  padding: 0 8px;
}

/* ============== 液态玻璃社交登录按钮 ============== */
.social-login {
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-bottom: 32px;
}

.social-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 14px;
  width: 100%;
  padding: 16px 24px;
  background: var(--glass-transparency);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-medium);
  color: var(--text-primary);
  font-family: 'Inter', sans-serif;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all var(--liquid-duration-normal) cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(15px) saturate(150%);
  position: relative;
  overflow: hidden;
  /* 液态玻璃社交按钮阴影 */
  box-shadow:
    0 6px 20px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 var(--glass-highlight);
}

/* 液态玻璃社交按钮内部光效 */
.social-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, var(--glass-highlight), transparent);
  transition: left var(--liquid-duration-slow);
}

.social-button:hover::before {
  left: 100%;
}

.social-button:hover {
  background: rgba(255, 255, 255, 0.06);
  border-color: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
  box-shadow:
    0 10px 30px rgba(0, 0, 0, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.12);
}

.social-button.google:hover {
  border-color: #4285f4;
  background: rgba(66, 133, 244, 0.05);
  box-shadow:
    0 10px 30px rgba(66, 133, 244, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.12);
}

.social-button.github:hover {
  border-color: #6e7681;
  background: rgba(110, 118, 129, 0.05);
  box-shadow:
    0 10px 30px rgba(110, 118, 129, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.12);
}

/* ============== 液态玻璃表单切换按钮 ============== */
.form-switch {
  text-align: center;
  margin-top: 12px;
}

.switch-button {
  position: relative;
  background: linear-gradient(135deg, rgba(0, 212, 255, 0.08), rgba(0, 212, 255, 0.04));
  border: 1px solid rgba(0, 212, 255, 0.25);
  border-radius: var(--radius-medium);
  padding: 14px 28px;
  color: var(--liquid-primary);
  font-family: 'Inter', sans-serif;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all var(--liquid-duration-normal) cubic-bezier(0.4, 0, 0.2, 1);
  text-decoration: none;
  backdrop-filter: blur(15px) saturate(150%);
  overflow: hidden;
  display: inline-flex;
  align-items: center;
  gap: 10px;
  box-shadow:
    0 6px 20px rgba(0, 212, 255, 0.1),
    inset 0 1px 0 var(--glass-highlight);
}

/* 液态玻璃切换按钮内部光效 */
.switch-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.12), transparent);
  transition: left var(--liquid-duration-slow);
}

.switch-button:hover::before {
  left: 100%;
}

.switch-button:hover {
  color: var(--text-primary);
  background: linear-gradient(135deg, rgba(0, 212, 255, 0.15), rgba(0, 212, 255, 0.08));
  border-color: var(--liquid-primary);
  transform: translateY(-2px);
  box-shadow:
    0 10px 30px rgba(0, 212, 255, 0.25),
    inset 0 1px 0 rgba(255, 255, 255, 0.15);
}

/* 液态玻璃切换按钮装饰元素 */
.switch-button::after {
  content: '✨';
  font-size: 12px;
  opacity: 0;
  transition: opacity var(--liquid-duration-normal);
  margin-left: 4px;
}

.switch-button:hover::after {
  opacity: 1;
}

/* 中文字体优化 */
.login-page[data-lang="zh"] .form-group label,
.login-page[data-lang="zh"] .form-group input,
.login-page[data-lang="zh"] .social-button,
.login-page[data-lang="zh"] .switch-button,
.login-page[data-lang="zh"] .back-button,
.login-page[data-lang="zh"] .language-toggle {
  font-family: 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
}

.login-page[data-lang="zh"] .welcome-text,
.login-page[data-lang="zh"] .brand-subtitle {
  font-family: 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
  letter-spacing: 0.5px;
}

/* 表单进度指示器 - 移除蓝色装饰条 */
.form-progress {
  display: none; /* 隐藏进度条 */
}

.form-progress-bar {
  display: none; /* 隐藏进度条 */
}

/* ============== 液态玻璃表单验证状态 ============== */
.form-group input.error {
  border-color: var(--error-color);
  background: rgba(239, 68, 68, 0.05);
  box-shadow:
    0 0 0 3px rgba(239, 68, 68, 0.15),
    0 4px 15px rgba(239, 68, 68, 0.1);
  animation: liquidShakeError 0.5s ease-in-out;
}

.form-group input.success {
  border-color: var(--success-color);
  background: rgba(16, 185, 129, 0.05);
  box-shadow:
    0 0 0 3px rgba(16, 185, 129, 0.15),
    0 4px 15px rgba(16, 185, 129, 0.1);
}

/* ============== 液态玻璃错误消息系统 ============== */
.error-message {
  color: var(--error-color);
  font-family: 'Inter', sans-serif;
  font-size: 11px;
  font-weight: 500;
  margin-top: 6px;
  display: block;
  animation: liquidErrorSlideIn 0.3s ease-out;
  opacity: 0.9;
  background: rgba(239, 68, 68, 0.05);
  padding: 4px 8px;
  border-radius: var(--radius-small);
  border-left: 2px solid var(--error-color);
}

/* 液态玻璃提交错误样式 */
.submit-error {
  background: linear-gradient(135deg, rgba(239, 68, 68, 0.08), rgba(239, 68, 68, 0.04));
  border: 1px solid rgba(239, 68, 68, 0.25);
  border-radius: var(--radius-medium);
  padding: 12px 16px;
  margin-bottom: 16px;
  backdrop-filter: blur(15px) saturate(150%);
  font-size: 13px;
  font-weight: 500;
  line-height: 1.4;
  box-shadow:
    0 4px 15px rgba(239, 68, 68, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.05);
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: center;
  gap: 10px;
  animation: liquidErrorSlideIn 0.4s ease-out;
  color: rgba(255, 255, 255, 0.9);
}

.submit-error svg {
  width: 14px;
  height: 14px;
  flex-shrink: 0;
  opacity: 0.8;
}

.submit-error::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 3px;
  height: 100%;
  background: linear-gradient(180deg, var(--error-color), rgba(255, 107, 122, 0.6));
  opacity: 0.7;
}

/* 新增：错误消息的悬停效果 */
.submit-error:hover {
  background: linear-gradient(135deg, rgba(255, 107, 122, 0.12), rgba(255, 107, 122, 0.06));
  border-color: rgba(255, 107, 122, 0.35);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(255, 107, 122, 0.15);
  transition: all 0.2s ease;
}

/* 新增：错误消息的关闭按钮 */
.error-dismiss {
  background: none;
  border: none;
  color: var(--error-color);
  cursor: pointer;
  padding: 2px;
  margin-left: auto;
  opacity: 0.6;
  border-radius: 4px;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.error-dismiss:hover {
  opacity: 1;
  background: rgba(255, 107, 122, 0.1);
  transform: scale(1.1);
}

.error-dismiss svg {
  width: 12px;
  height: 12px;
}

/* 新增：不同类型的错误消息样式 */
.submit-error.error-critical {
  border-color: rgba(255, 71, 87, 0.4);
  background: linear-gradient(135deg, rgba(255, 71, 87, 0.12), rgba(255, 71, 87, 0.06));
  animation: slideInError 0.3s ease-out, errorPulse 2s ease-in-out infinite;
}

.submit-error.error-warning {
  border-color: rgba(255, 193, 7, 0.4);
  background: linear-gradient(135deg, rgba(255, 193, 7, 0.12), rgba(255, 193, 7, 0.06));
  color: #ffc107;
}

.submit-error.error-warning::before {
  background: linear-gradient(180deg, #ffc107, rgba(255, 193, 7, 0.6));
}

.submit-error.error-info {
  border-color: rgba(33, 150, 243, 0.4);
  background: linear-gradient(135deg, rgba(33, 150, 243, 0.12), rgba(33, 150, 243, 0.06));
  color: #2196f3;
}

.submit-error.error-info::before {
  background: linear-gradient(180deg, #2196f3, rgba(33, 150, 243, 0.6));
}

/* 新增：错误消息的淡出状态 */
.submit-error.error-dismissing {
  animation: slideOutError 0.3s ease-out forwards;
}

/* 新增：字段级错误消息的增强样式 */
.error-message.field-error {
  background: rgba(255, 107, 122, 0.05);
  padding: 2px 6px;
  border-radius: 4px;
  border-left: 2px solid var(--error-color);
  margin-top: 2px;
}

/* 中文错误消息字体优化 */
.login-page[data-lang="zh"] .error-message,
.login-page[data-lang="zh"] .submit-error {
  font-family: 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
  letter-spacing: 0.3px;
}

/* ============== 液态玻璃动画系统 ============== */
@keyframes liquidErrorSlideIn {
  from {
    opacity: 0;
    transform: translateX(-15px) scale(0.95);
    filter: blur(2px);
  }
  to {
    opacity: 1;
    transform: translateX(0) scale(1);
    filter: blur(0);
  }
}

@keyframes liquidShakeError {
  0%, 100% {
    transform: scale(1) translateX(0);
  }
  25% {
    transform: scale(1.02) translateX(-2px);
  }
  75% {
    transform: scale(0.98) translateX(2px);
  }
}

@keyframes liquidFadeInError {
  from {
    opacity: 0;
    transform: translateY(-10px) scale(0.9);
    filter: blur(1px);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
    filter: blur(0);
  }
}

/* 新增：错误消息脉冲动画 */
@keyframes errorPulse {
  0% { box-shadow: 0 2px 8px rgba(255, 107, 122, 0.1); }
  50% { box-shadow: 0 2px 12px rgba(255, 107, 122, 0.2); }
  100% { box-shadow: 0 2px 8px rgba(255, 107, 122, 0.1); }
}

/* 新增：错误消息淡出动画 */
@keyframes slideOutError {
  from {
    opacity: 1;
    transform: translateX(0) scale(1);
    max-height: 50px;
    margin-bottom: 12px;
  }
  to {
    opacity: 0;
    transform: translateX(-10px) scale(0.95);
    max-height: 0;
    margin-bottom: 0;
  }
}

/* 增强的动画效果 */
.login-container {
  animation: fadeInUp 0.8s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 自定义滚动条 */
.form-container::-webkit-scrollbar {
  width: 6px;
}

.form-container::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 3px;
}

.form-container::-webkit-scrollbar-thumb {
  background: rgba(77, 200, 255, 0.3);
  border-radius: 3px;
  transition: background 0.3s ease;
}

.form-container::-webkit-scrollbar-thumb:hover {
  background: rgba(77, 200, 255, 0.5);
}

/* 表单分组样式 */
.form-section-divider {
  margin: 24px 0;
  text-align: center;
  position: relative;
}

.form-section-divider::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(77, 200, 255, 0.3), transparent);
}

.form-section-title {
  background: rgba(15, 25, 40, 0.9);
  padding: 0 12px;
  font-family: 'Rajdhani', sans-serif;
  font-size: 12px;
  color: rgba(255, 255, 255, 0.6);
  text-transform: uppercase;
  letter-spacing: 1px;
  position: relative;
  z-index: 1;
}



/* ============== 液态玻璃响应式设计 ============== */
@media screen and (max-width: 1024px) {
  .login-content {
    grid-template-columns: 1fr;
    gap: 50px;
    max-width: 650px;
    min-height: auto;
  }

  .brand-section {
    order: 2;
    padding: 30px;
    text-align: center;
    animation-delay: 0.1s;
  }

  .form-section {
    order: 1;
    animation-delay: 0.3s;
  }

  .form-container {
    max-width: 100%;
    margin: 0 auto;
    padding: 35px;
  }

  .app-name {
    font-size: 42px;
    letter-spacing: 2px;
  }

  .welcome-text {
    font-size: 22px;
  }

  /* 调整点阵背景密度 */
  .login-page::before {
    background-size: 35px 35px, 70px 70px;
  }
}

@media screen and (max-width: 768px) {
  .login-container {
    padding: 80px 20px 20px;
    min-height: 100vh;
  }

  .floating-nav-buttons {
    top: 18px;
    left: 18px;
    right: 18px;
  }

  .floating-back-button,
  .floating-language-toggle {
    padding: 11px 18px;
    font-size: 13px;
    border-radius: var(--radius-small);
  }

  .language-dropdown-menu {
    min-width: 150px;
  }

  .form-container {
    padding: 30px;
    border-radius: var(--radius-large);
    max-height: 90vh;
  }

  .form-title {
    font-size: 24px;
    letter-spacing: 1px;
  }

  .app-name {
    font-size: 32px;
    letter-spacing: 1.5px;
  }

  .welcome-text {
    font-size: 20px;
  }

  .brand-subtitle {
    font-size: 17px;
  }

  .form-group {
    margin-bottom: 20px;
  }

  .form-group input {
    padding: 15px 20px;
    padding-right: 50px;
    font-size: 14px;
  }

  .submit-button {
    padding: 16px 24px;
    font-size: 15px;
  }

  /* 移动端点阵背景优化 */
  .login-page::before {
    background-size: 30px 30px, 60px 60px;
    opacity: 0.3;
  }

  .login-page::after {
    background-size: 100px 100px;
    opacity: 0.5;
  }
}

@media screen and (max-width: 480px) {
  .login-container {
    padding: 70px 15px 15px;
  }

  .floating-nav-buttons {
    top: 15px;
    left: 15px;
    right: 15px;
  }

  .floating-back-button,
  .floating-language-toggle {
    padding: 10px 14px;
    font-size: 12px;
    gap: 8px;
  }

  .back-icon,
  .language-icon {
    font-size: 16px;
  }

  .language-dropdown-menu {
    min-width: 130px;
  }

  .language-option {
    padding: 11px 14px;
    font-size: 13px;
  }

  .form-container {
    padding: 25px;
    border-radius: var(--radius-medium);
  }

  .form-title {
    font-size: 22px;
  }

  .app-name {
    font-size: 28px;
    letter-spacing: 1px;
  }

  .welcome-text {
    font-size: 18px;
  }

  .brand-subtitle {
    font-size: 15px;
  }

  .form-group input {
    padding: 14px 18px;
    padding-right: 45px;
  }

  .social-login {
    gap: 12px;
  }

  .social-button {
    padding: 12px 16px;
    font-size: 13px;
  }

  /* 小屏幕错误消息优化 */
  .submit-error {
    padding: 8px 12px;
    font-size: 12px;
    margin-bottom: 12px;
  }

  .submit-error svg {
    width: 13px;
    height: 13px;
  }

  .error-message {
    font-size: 10px;
  }

  /* 小屏幕点阵背景优化 */
  .login-page::before {
    background-size: 25px 25px, 50px 50px;
    opacity: 0.25;
  }

  .login-page::after {
    background-size: 80px 80px;
    opacity: 0.4;
  }
}

/* 动画增强 - 修复横向移动问题 */
/* fadeInUp 动画已在上方定义，这里只保留动画应用 */

.form-container {
  animation: fadeInUp 0.6s ease-out 0.2s both;
}

.brand-section {
  animation: fadeInUp 0.6s ease-out both;
}


